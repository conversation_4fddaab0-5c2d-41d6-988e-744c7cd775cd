

### 测试使用多个文件URL1111111111111111111111111111111111
POST http://192.168.113.90:88/v1/chat-messages
Authorization: Bearer app-VOPtG3HMcb2Mwt1QqY7hS4sa
Content-Type: application/json

{
    "inputs": {
        "outputTemplate": {
            "type": "document",
            "transfer_method": "remote_url",
            "url": "http://192.168.113.53:9000/langwell-default/langwell_/2025/05/16/690112cbf03e45a7ad2aca1075607d65.docx"
        },
        "length": "不超过200字",
        "lang": "中文(简体)",
        "tone": "详细的",
        "format":"段落"
    },
    "response_mode": "streaming",
    "user": "610275098545612802",
    "conversation_id": "",
    "query": "22"
}

### 测试使用Word文档URL
POST http://192.168.113.90:88/v1/chat-messages
Authorization: Bearer app-qXRXokkE7CtkyK2htwk6nUD6
Content-Type: application/json

{
    "inputs": {},
    "query": "总结这个Word文档的内容",
    "response_mode": "streaming",
    "conversation_id": "",
    "user": "abc-123",
    "files": [
      {
        "type": "file",
        "transfer_method": "remote_url",
        "url": "http://192.168.113.53:9000/langwell-default/langwell-default/2024/11/20/document.docx"
      }
    ]
}

### 测试使用PDF文档URL
POST http://192.168.113.90:88/v1/chat-messages
Authorization: Bearer app-qXRXokkE7CtkyK2htwk6nUD6
Content-Type: application/json

{
    "inputs": {},
    "query": "提取这个PDF文档中的关键信息",
    "response_mode": "streaming",
    "conversation_id": "",
    "user": "abc-123",
    "files": [
      {
        "type": "file",
        "transfer_method": "remote_url",
        "url": "http://192.168.113.53:9000/langwell-default/langwell-default/2024/11/20/document.pdf"
      }
    ]
}
