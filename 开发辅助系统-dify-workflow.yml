app:
  description: "智能化开发辅助系统，支持从需求分析到测试用例的完整开发文档生成流程，具备智能分块处理和进度管理功能"
  icon: "🚀"
  icon_background: "#E3F2FD"
  mode: advanced-chat
  name: 开发辅助系统
  use_icon_as_answer_icon: false

dependencies:
  - current_identifier: null
    type: marketplace
    value:
      marketplace_plugin_unique_identifier: langgenius/zhipuai:0.0.7@1ee8fe156cc3dffcd085d7fc5581395aecf667cfb548c8d621e505b8a160b619
  - current_identifier: null
    type: marketplace
    value:
      marketplace_plugin_unique_identifier: langgenius/siliconflow:0.0.12@2ab1fcd77138b7ecdd707790aa1936d5c187fca547ebd165728237b0630c3a44

kind: app
version: 0.1.3

workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
        - .doc
        - .docx
        - .pdf
        - .txt
        - .md
        - .xlsx
      allowed_file_types:
        - document
      allowed_file_upload_methods:
        - local_file
        - remote_url
      enabled: true
      number_limits: 5
    opening_statement: "欢迎使用开发辅助系统！我可以帮您从需求分析到测试用例生成完整的开发文档。请选择起始阶段或上传相关文档开始。"
    suggested_questions:
      - "我想从零开始，有一个新的产品需求"
      - "我已经有功能清单，需要生成原型设计"
      - "帮我设计数据库表结构"
      - "生成API接口文档"
    suggested_questions_after_answer:
      enabled: true
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    text_to_speech:
      enabled: false

  graph:
    nodes:
      # 开始节点
      - data:
          desc: "定义项目基本信息和起始阶段"
          selected: false
          title: 开始
          type: start
          variables:
            - label: 项目名称
              max_length: 100
              required: true
              type: text-input
              variable: project_name
            - label: 项目类型
              options:
                - web
                - mobile
                - api
                - desktop
              required: true
              type: select
              variable: project_type
            - label: 项目规模
              options:
                - small
                - medium
                - large
              required: true
              type: select
              variable: project_scale
            - label: 起始阶段
              options:
                - requirements
                - functions
                - prototype
                - database
                - api
                - testing
              required: true
              type: select
              variable: start_stage
            - label: 需求描述
              max_length: 2000
              required: false
              type: paragraph
              variable: requirements_input
            - label: 技术栈偏好
              max_length: 200
              required: false
              type: text-input
              variable: tech_stack
        height: 120
        id: "start"
        position:
          x: 30
          y: 258
        positionAbsolute:
          x: 30
          y: 258
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 280

      # 项目复杂度评估节点
      - data:
          desc: "评估项目复杂度，选择合适的生成策略"
          context:
            enabled: false
            variable_selector: []
          model:
            completion_params:
              temperature: 0.3
              max_tokens: 500
            mode: chat
            name: Pro/deepseek-ai/DeepSeek-V3
            provider: langgenius/siliconflow/siliconflow
          prompt_template:
            - id: "complexity-assessment"
              role: system
              text: |
                你是一个专业的项目评估专家。请根据以下信息评估项目复杂度：

                项目名称：{{#start.project_name#}}
                项目类型：{{#start.project_type#}}
                项目规模：{{#start.project_scale#}}
                需求描述：{{#start.requirements_input#}}
                技术栈：{{#start.tech_stack#}}

                请评估项目复杂度并输出JSON格式：
                {
                  "complexity_level": "simple|medium|complex",
                  "estimated_modules": 数字,
                  "estimated_functions": 数字,
                  "recommended_strategy": "full|overview|batch",
                  "reasoning": "评估理由"
                }

                评估标准：
                - simple: 模块数≤5，功能数≤30，推荐full策略
                - medium: 模块数6-10，功能数31-100，推荐overview策略  
                - complex: 模块数>10，功能数>100，推荐batch策略
          selected: false
          title: 项目复杂度评估
          type: llm
          variables: []
          vision:
            enabled: false
        height: 54
        id: "complexity-assessment"
        position:
          x: 380
          y: 258
        positionAbsolute:
          x: 380
          y: 258
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244

      # 阶段选择判断节点
      - data:
          cases:
            - case_id: "requirements"
              conditions:
                - comparison_operator: is
                  id: "stage-condition-1"
                  value: "requirements"
                  varType: string
                  variable_selector:
                    - start
                    - start_stage
              id: "requirements"
              logical_operator: and
            - case_id: "functions"
              conditions:
                - comparison_operator: is
                  id: "stage-condition-2"
                  value: "functions"
                  varType: string
                  variable_selector:
                    - start
                    - start_stage
              id: "functions"
              logical_operator: and
            - case_id: "prototype"
              conditions:
                - comparison_operator: is
                  id: "stage-condition-3"
                  value: "prototype"
                  varType: string
                  variable_selector:
                    - start
                    - start_stage
              id: "prototype"
              logical_operator: and
            - case_id: "database"
              conditions:
                - comparison_operator: is
                  id: "stage-condition-4"
                  value: "database"
                  varType: string
                  variable_selector:
                    - start
                    - start_stage
              id: "database"
              logical_operator: and
            - case_id: "api"
              conditions:
                - comparison_operator: is
                  id: "stage-condition-5"
                  value: "api"
                  varType: string
                  variable_selector:
                    - start
                    - start_stage
              id: "api"
              logical_operator: and
            - case_id: "testing"
              conditions:
                - comparison_operator: is
                  id: "stage-condition-6"
                  value: "testing"
                  varType: string
                  variable_selector:
                    - start
                    - start_stage
              id: "testing"
              logical_operator: and
          desc: "根据用户选择的起始阶段进行路由"
          selected: false
          title: 阶段选择判断
          type: if-else
        height: 54
        id: "stage-selector"
        position:
          x: 680
          y: 258
        positionAbsolute:
          x: 680
          y: 258
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244

      # 需求拆解LLM节点
      - data:
          desc: "将原始需求转换为结构化需求文档"
          context:
            enabled: false
            variable_selector: []
          model:
            completion_params:
              temperature: 0.3
              max_tokens: 3000
            mode: chat
            name: Pro/deepseek-ai/DeepSeek-V3
            provider: langgenius/siliconflow/siliconflow
          prompt_template:
            - id: "requirements-analysis"
              role: system
              text: |
                你是一个专业的需求分析师。请将以下原始需求转换为结构化的需求文档：

                项目信息：
                - 项目名称：{{#start.project_name#}}
                - 项目类型：{{#start.project_type#}}
                - 项目规模：{{#start.project_scale#}}
                - 技术栈：{{#start.tech_stack#}}

                原始需求：
                {{#start.requirements_input#}}

                复杂度评估：
                {{#complexity-assessment.text#}}

                请按以下格式输出，控制在3000 tokens以内：

                ## 功能需求
                - FR001: [功能名称] - [详细描述]
                - FR002: [功能名称] - [详细描述]

                ## 非功能需求
                - NFR001: [类型] - [具体要求]
                - NFR002: [类型] - [具体要求]

                ## 约束条件
                - CON001: [约束类型] - [具体约束]
                - CON002: [约束类型] - [具体约束]

                ## 用户角色
                - 角色1：[角色描述]
                - 角色2：[角色描述]

                请确保需求描述清晰、具体、可测量。如果原始需求信息不完整，请列出需要补充的具体信息。
          selected: false
          title: 需求拆解
          type: llm
          variables: []
          vision:
            enabled: false
        height: 54
        id: "requirements-analysis"
        position:
          x: 980
          y: 158
        positionAbsolute:
          x: 980
          y: 158
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244

      # 功能清单生成节点
      - data:
          desc: "基于需求分析生成详细功能清单"
          context:
            enabled: false
            variable_selector: []
          model:
            completion_params:
              temperature: 0.3
              max_tokens: 3000
            mode: chat
            name: Pro/deepseek-ai/DeepSeek-V3
            provider: langgenius/siliconflow/siliconflow
          prompt_template:
            - id: "function-list"
              role: system
              text: |
                基于以下需求分析结果，生成功能清单：

                需求分析结果：
                {{#requirements-analysis.text#}}

                项目信息：
                - 项目名称：{{#start.project_name#}}
                - 项目类型：{{#start.project_type#}}
                - 复杂度评估：{{#complexity-assessment.text#}}

                请按以下格式输出功能清单，控制在3000 tokens以内：

                ## 功能清单

                ### 模块：[模块名称]
                - UC001: [功能名称] [P0] - [详细描述和验收标准]
                - UC002: [功能名称] [P1] - [详细描述和验收标准]

                ### 模块：[模块名称]
                - DC001: [功能名称] [P0] - [详细描述和验收标准]
                - DC002: [功能名称] [P1] - [详细描述和验收标准]

                优先级说明：
                - P0: 核心功能，必须实现
                - P1: 重要功能，优先实现
                - P2: 一般功能，后续实现

                请确保：
                1. 功能编号唯一且有规律
                2. 功能描述包含验收标准
                3. 优先级分配合理
                4. 按模块分类组织
                5. 覆盖所有需求点
          selected: false
          title: 功能清单生成
          type: llm
          variables: []
          vision:
            enabled: false
        height: 54
        id: "function-list"
        position:
          x: 980
          y: 258
        positionAbsolute:
          x: 980
          y: 258
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244

      # 原型设计节点
      - data:
          desc: "基于功能清单生成页面原型设计"
          context:
            enabled: false
            variable_selector: []
          model:
            completion_params:
              temperature: 0.4
              max_tokens: 3000
            mode: chat
            name: Pro/deepseek-ai/DeepSeek-V3
            provider: langgenius/siliconflow/siliconflow
          prompt_template:
            - id: "prototype-design"
              role: system
              text: |
                基于以下功能清单，生成页面原型设计说明：

                功能清单：
                {{#function-list.text#}}

                项目信息：
                - 项目名称：{{#start.project_name#}}
                - 项目类型：{{#start.project_type#}}

                请按以下格式输出原型设计，控制在3000 tokens以内：

                ## 页面原型设计

                ### 页面：[页面名称]
                **页面布局：**
                - 页面结构：[描述页面整体布局]
                - 区域划分：[描述各功能区域]
                - 元素位置：[描述关键元素位置]

                **交互流程：**
                1. [用户操作步骤1]
                2. [用户操作步骤2]
                3. [页面跳转关系]

                **UI组件规格：**
                - 表单字段：[字段名称、类型、验证规则]
                - 按钮样式：[按钮文案、样式、状态]
                - 数据展示：[表格、列表、卡片等展示方式]

                **响应式设计：**
                - 桌面端：[布局说明]
                - 移动端：[适配说明]

                请确保设计符合用户体验最佳实践。
          selected: false
          title: 原型设计
          type: llm
          variables: []
          vision:
            enabled: false
        height: 54
        id: "prototype-design"
        position:
          x: 980
          y: 358
        positionAbsolute:
          x: 980
          y: 358
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244

      # 数据库设计节点
      - data:
          desc: "基于功能需求生成数据库设计方案"
          context:
            enabled: false
            variable_selector: []
          model:
            completion_params:
              temperature: 0.3
              max_tokens: 3000
            mode: chat
            name: Pro/deepseek-ai/DeepSeek-V3
            provider: langgenius/siliconflow/siliconflow
          prompt_template:
            - id: "database-design"
              role: system
              text: |
                基于以下功能需求和原型设计，生成数据库设计方案：

                功能清单：
                {{#function-list.text#}}

                原型设计：
                {{#prototype-design.text#}}

                项目信息：
                - 项目名称：{{#start.project_name#}}
                - 技术栈：{{#start.tech_stack#}}

                请按以下格式输出数据库设计，控制在3000 tokens以内：

                ## 数据库设计

                ### 表：[表名]
                ```sql
                CREATE TABLE `table_name` (
                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                  `field_name` varchar(100) NOT NULL COMMENT '字段描述',
                  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
                  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                  PRIMARY KEY (`id`),
                  UNIQUE KEY `uk_field` (`field_name`),
                  KEY `idx_status` (`status`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表描述';
                ```

                **表关系说明：**
                - 与其他表的关联关系
                - 外键约束说明

                **索引策略：**
                - 主键索引
                - 唯一索引
                - 普通索引

                请确保：
                1. 表结构规范化
                2. 字段类型合理
                3. 索引设计优化
                4. 注释完整清晰
          selected: false
          title: 数据库设计
          type: llm
          variables: []
          vision:
            enabled: false
        height: 54
        id: "database-design"
        position:
          x: 980
          y: 458
        positionAbsolute:
          x: 980
          y: 458
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244

      # API接口设计节点
      - data:
          desc: "基于功能清单和数据库设计生成API接口文档"
          context:
            enabled: false
            variable_selector: []
          model:
            completion_params:
              temperature: 0.3
              max_tokens: 3000
            mode: chat
            name: Pro/deepseek-ai/DeepSeek-V3
            provider: langgenius/siliconflow/siliconflow
          prompt_template:
            - id: "api-design"
              role: system
              text: |
                基于以下功能清单和数据库设计，生成RESTful API接口文档：

                功能清单：
                {{#function-list.text#}}

                数据库设计：
                {{#database-design.text#}}

                项目信息：
                - 项目名称：{{#start.project_name#}}
                - 技术栈：{{#start.tech_stack#}}

                请按以下格式输出API接口文档，控制在3000 tokens以内：

                ## API接口文档

                ### 接口：[接口名称]
                **基本信息：**
                - 请求方法：POST/GET/PUT/DELETE
                - 请求路径：/api/v1/resource
                - 功能描述：[接口功能说明]

                **请求参数：**
                ```json
                {
                  "field_name": "string, 必填, 字段描述（验证规则）",
                  "field_name2": "number, 可选, 字段描述"
                }
                ```

                **响应示例：**
                成功响应（200）：
                ```json
                {
                  "code": 200,
                  "message": "操作成功",
                  "data": {
                    "id": 1001,
                    "field_name": "value"
                  }
                }
                ```

                **错误响应：**
                - 400: 参数错误 - [具体错误说明]
                - 401: 未授权 - [认证失败]
                - 404: 资源不存在 - [资源未找到]
                - 500: 服务器错误 - [系统内部错误]

                请确保接口设计符合RESTful规范。
          selected: false
          title: API接口设计
          type: llm
          variables: []
          vision:
            enabled: false
        height: 54
        id: "api-design"
        position:
          x: 980
          y: 558
        positionAbsolute:
          x: 980
          y: 558
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244

      # 测试用例生成节点
      - data:
          desc: "基于功能清单和接口设计生成测试用例"
          context:
            enabled: false
            variable_selector: []
          model:
            completion_params:
              temperature: 0.3
              max_tokens: 3000
            mode: chat
            name: Pro/deepseek-ai/DeepSeek-V3
            provider: langgenius/siliconflow/siliconflow
          prompt_template:
            - id: "test-cases"
              role: system
              text: |
                基于以下功能清单和API接口设计，生成完整的测试用例：

                功能清单：
                {{#function-list.text#}}

                API接口设计：
                {{#api-design.text#}}

                项目信息：
                - 项目名称：{{#start.project_name#}}

                请按以下格式输出测试用例，控制在3000 tokens以内：

                ## 测试用例

                ### 测试用例ID: TC001
                **测试模块：** [模块名称]
                **测试功能：** [功能名称]
                **优先级：** P0/P1/P2

                **前置条件：**
                - 系统正常运行
                - 数据库连接正常
                - [其他前置条件]

                **测试步骤：**
                1. [操作步骤1]
                2. [操作步骤2]
                3. [操作步骤3]

                **预期结果：**
                - [预期结果1]
                - [预期结果2]
                - [预期结果3]

                **测试数据：**
                - 输入数据：[具体测试数据]
                - 边界值：[边界测试数据]
                - 异常数据：[异常测试数据]

                **接口测试：**
                - 请求URL：[API地址]
                - 请求参数：[参数值]
                - 预期响应：[响应结果]

                请确保测试用例覆盖正常流程、异常流程和边界条件。
          selected: false
          title: 测试用例生成
          type: llm
          variables: []
          vision:
            enabled: false
        height: 54
        id: "test-cases"
        position:
          x: 980
          y: 658
        positionAbsolute:
          x: 980
          y: 658
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244

      # 文档整合节点
      - data:
          desc: "整合所有生成的文档为完整的开发文档包"
          selected: false
          template: |
            # 开发文档

            ## 项目信息
            - **项目名称**：{{project_name}}
            - **项目类型**：{{project_type}}
            - **项目规模**：{{project_scale}}
            - **技术栈**：{{tech_stack}}

            ## 复杂度评估
            {{complexity_assessment}}

            ## 需求分析
            {{requirements_analysis}}

            ## 功能清单
            {{function_list}}

            ## 原型设计
            {{prototype_design}}

            ## 数据库设计
            {{database_design}}

            ## API接口文档
            {{api_design}}

            ## 测试用例
            {{test_cases}}

            ---
            *本文档由开发辅助系统自动生成*
          title: 文档整合
          type: template-transform
          variables:
            - value_selector:
                - start
                - project_name
              variable: project_name
            - value_selector:
                - start
                - project_type
              variable: project_type
            - value_selector:
                - start
                - project_scale
              variable: project_scale
            - value_selector:
                - start
                - tech_stack
              variable: tech_stack
            - value_selector:
                - complexity-assessment
                - text
              variable: complexity_assessment
            - value_selector:
                - requirements-analysis
                - text
              variable: requirements_analysis
            - value_selector:
                - function-list
                - text
              variable: function_list
            - value_selector:
                - prototype-design
                - text
              variable: prototype_design
            - value_selector:
                - database-design
                - text
              variable: database_design
            - value_selector:
                - api-design
                - text
              variable: api_design
            - value_selector:
                - test-cases
                - text
              variable: test_cases
        height: 54
        id: "document-integration"
        position:
          x: 1280
          y: 458
        positionAbsolute:
          x: 1280
          y: 458
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244

      # 最终回答节点
      - data:
          answer: |
            ## 🎉 开发文档生成完成！

            您的项目开发文档已经生成完成。

            ### 📋 生成内容包括：
            - ✅ 项目复杂度评估
            - ✅ 需求分析文档
            - ✅ 功能清单设计
            - ✅ 页面原型设计
            - ✅ 数据库设计方案
            - ✅ API接口文档
            - ✅ 测试用例设计

            ### 📄 完整文档：
            {{document-integration.output}}

            ### 💡 后续建议：
            1. 请仔细审查生成的文档内容
            2. 根据实际情况调整和完善细节
            3. 与团队成员讨论确认技术方案
            4. 可以基于此文档开始开发工作

            如需修改或补充任何部分，请告诉我具体需求！
          desc: ""
          selected: false
          title: 最终回答
          type: answer
          variables: []
        height: 54
        id: "final-answer"
        position:
          x: 1580
          y: 458
        positionAbsolute:
          x: 1580
          y: 458
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244

    edges:
      # 主流程连接
      - data:
          isInIteration: false
          sourceType: start
          targetType: llm
        id: "start-source-complexity-assessment-target"
        source: "start"
        sourceHandle: source
        target: "complexity-assessment"
        targetHandle: target
        type: custom
        zIndex: 0

      - data:
          isInIteration: false
          sourceType: llm
          targetType: if-else
        id: "complexity-assessment-source-stage-selector-target"
        source: "complexity-assessment"
        sourceHandle: source
        target: "stage-selector"
        targetHandle: target
        type: custom
        zIndex: 0

      # 阶段分支连接
      - data:
          isInIteration: false
          sourceType: if-else
          targetType: llm
        id: "stage-selector-requirements-requirements-analysis-target"
        source: "stage-selector"
        sourceHandle: "requirements"
        target: "requirements-analysis"
        targetHandle: target
        type: custom
        zIndex: 0

      - data:
          isInIteration: false
          sourceType: if-else
          targetType: llm
        id: "stage-selector-functions-function-list-target"
        source: "stage-selector"
        sourceHandle: "functions"
        target: "function-list"
        targetHandle: target
        type: custom
        zIndex: 0

      - data:
          isInIteration: false
          sourceType: if-else
          targetType: llm
        id: "stage-selector-prototype-prototype-design-target"
        source: "stage-selector"
        sourceHandle: "prototype"
        target: "prototype-design"
        targetHandle: target
        type: custom
        zIndex: 0

      - data:
          isInIteration: false
          sourceType: if-else
          targetType: llm
        id: "stage-selector-database-database-design-target"
        source: "stage-selector"
        sourceHandle: "database"
        target: "database-design"
        targetHandle: target
        type: custom
        zIndex: 0

      - data:
          isInIteration: false
          sourceType: if-else
          targetType: llm
        id: "stage-selector-api-api-design-target"
        source: "stage-selector"
        sourceHandle: "api"
        target: "api-design"
        targetHandle: target
        type: custom
        zIndex: 0

      - data:
          isInIteration: false
          sourceType: if-else
          targetType: llm
        id: "stage-selector-testing-test-cases-target"
        source: "stage-selector"
        sourceHandle: "testing"
        target: "test-cases"
        targetHandle: target
        type: custom
        zIndex: 0

      # 顺序流程连接
      - data:
          isInIteration: false
          sourceType: llm
          targetType: llm
        id: "requirements-analysis-source-function-list-target"
        source: "requirements-analysis"
        sourceHandle: source
        target: "function-list"
        targetHandle: target
        type: custom
        zIndex: 0

      - data:
          isInIteration: false
          sourceType: llm
          targetType: llm
        id: "function-list-source-prototype-design-target"
        source: "function-list"
        sourceHandle: source
        target: "prototype-design"
        targetHandle: target
        type: custom
        zIndex: 0

      - data:
          isInIteration: false
          sourceType: llm
          targetType: llm
        id: "prototype-design-source-database-design-target"
        source: "prototype-design"
        sourceHandle: source
        target: "database-design"
        targetHandle: target
        type: custom
        zIndex: 0

      - data:
          isInIteration: false
          sourceType: llm
          targetType: llm
        id: "database-design-source-api-design-target"
        source: "database-design"
        sourceHandle: source
        target: "api-design"
        targetHandle: target
        type: custom
        zIndex: 0

      - data:
          isInIteration: false
          sourceType: llm
          targetType: llm
        id: "api-design-source-test-cases-target"
        source: "api-design"
        sourceHandle: source
        target: "test-cases"
        targetHandle: target
        type: custom
        zIndex: 0

      - data:
          isInIteration: false
          sourceType: llm
          targetType: template-transform
        id: "test-cases-source-document-integration-target"
        source: "test-cases"
        sourceHandle: source
        target: "document-integration"
        targetHandle: target
        type: custom
        zIndex: 0

      - data:
          isInIteration: false
          sourceType: template-transform
          targetType: answer
        id: "document-integration-source-final-answer-target"
        source: "document-integration"
        sourceHandle: source
        target: "final-answer"
        targetHandle: target
        type: custom
        zIndex: 0

    viewport:
      x: 0
      y: 0
      zoom: 0.6
