app:
  description: Adaptive RAG：在self RAG的基础上增加问题路由，非知识库相关问题，路由到web搜索。
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: 【测试】Adaptive-RAG
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/tavily:0.0.5@caf7214ca1a237845e991615fd12e04c8db8b703d59773538fe243f3de4044e9
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/siliconflow:0.0.8@217f973bd7ced1b099c2f0c669f1356bdf4cc38b8372fd58d7874f9940b95de3
kind: app
version: 0.1.5
workflow:
  conversation_variables:
  - description: 最后结果
    id: 7f5820d2-07a4-473a-bcc8-22b6ffa2a56f
    name: finallAnswer
    selector:
    - conversation
    - finallAnswer
    value: ''
    value_type: string
  - description: 评分为YES的chunks
    id: 9d65dcba-98d9-4345-a7f5-0eea6423e309
    name: filtered_docs
    selector:
    - conversation
    - filtered_docs
    value: ''
    value_type: string
  - description: 基于原始问题进行重写。
    id: 185c140e-c763-4154-9e4a-9763d5dbc606
    name: reQuery
    selector:
    - conversation
    - reQuery
    value: ''
    value_type: string
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: code
        targetType: knowledge-retrieval
      id: 1744614486889-source-1744614478508-target
      source: '1744614486889'
      sourceHandle: source
      target: '1744614478508'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: knowledge-retrieval
        targetType: code
      id: 1744614478508-source-1744615311611-target
      source: '1744614478508'
      sourceHandle: source
      target: '1744615311611'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: code
        targetType: llm
      id: 1744615311611-source-1744615075452-target
      source: '1744615311611'
      sourceHandle: source
      target: '1744615075452'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: llm
        targetType: assigner
      id: 1744616236018-source-1744616409947-target
      source: '1744616236018'
      sourceHandle: source
      target: '1744616409947'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: llm
        targetType: llm
      id: 1744616339334-source-1744616451373-target
      source: '1744616339334'
      sourceHandle: source
      target: '1744616451373'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: llm
        targetType: if-else
      id: 1744616451373-source-1744616501416-target
      source: '1744616451373'
      sourceHandle: source
      target: '1744616501416'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: llm
        targetType: if-else
      id: 1744616675645-source-1744616720788-target
      source: '1744616675645'
      sourceHandle: source
      target: '1744616720788'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: if-else
        targetType: llm
      id: 1744616501416-false-1744616675645-target
      source: '1744616501416'
      sourceHandle: 'false'
      target: '1744616675645'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: if-else
        targetType: llm
      id: 1744616720788-true-1744616236018-target
      source: '1744616720788'
      sourceHandle: 'true'
      target: '1744616236018'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: loop-start
        targetType: if-else
      id: 1744614444681start-source-1744617750070-target
      source: 1744614444681start
      sourceHandle: source
      target: '1744617750070'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: if-else
        targetType: code
      id: 1744617750070-true-1744614486889-target
      source: '1744617750070'
      sourceHandle: 'true'
      target: '1744614486889'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: if-else
        targetType: llm
      id: 1744617750070-false-1744616339334-target
      source: '1744617750070'
      sourceHandle: 'false'
      target: '1744616339334'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: assigner
        targetType: llm
      id: 1744618887928-source-1744616339334-target
      source: '1744618887928'
      sourceHandle: source
      target: '1744616339334'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: if-else
        targetType: assigner
      id: 1744616501416-true-1744619138669-target
      source: '1744616501416'
      sourceHandle: 'true'
      target: '1744619138669'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: if-else
        targetType: assigner
      id: 1744616720788-false-1744619976993-target
      source: '1744616720788'
      sourceHandle: 'false'
      target: '1744619976993'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: loop
        targetType: if-else
      id: 1744614444681-source-1744620253642-target
      source: '1744614444681'
      sourceHandle: source
      target: '1744620253642'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: if-else
        targetType: answer
      id: 1744620253642-true-1744620274008-target
      source: '1744620253642'
      sourceHandle: 'true'
      target: '1744620274008'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: if-else
        targetType: answer
      id: 1744620253642-false-1744620284607-target
      source: '1744620253642'
      sourceHandle: 'false'
      target: '1744620284607'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1744610481918-source-1744623005945-target
      source: '1744610481918'
      sourceHandle: source
      target: '1744623005945'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: if-else
      id: 1744623005945-source-1744623102407-target
      source: '1744623005945'
      sourceHandle: source
      target: '1744623102407'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: loop
      id: 1744623102407-true-1744614444681-target
      source: '1744623102407'
      sourceHandle: 'true'
      target: '1744614444681'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: if-else
        targetType: tool
      id: 1744623102407-false-1744623172505-target
      source: '1744623102407'
      sourceHandle: 'false'
      target: '1744623172505'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: 1744623172505-source-17446232505850-target
      source: '1744623172505'
      sourceHandle: source
      target: '17446232505850'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 17446232505850-source-1744623298915-target
      source: '17446232505850'
      sourceHandle: source
      target: '1744623298915'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: llm
        targetType: assigner
      id: 1744615075452-source-1744618887928-target
      source: '1744615075452'
      sourceHandle: source
      target: '1744618887928'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: answer
        targetType: assigner
      id: 1744620274008-source-1744679036603-target
      source: '1744620274008'
      sourceHandle: source
      target: '1744679036603'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: answer
        targetType: assigner
      id: 1744620284607-source-1744679036603-target
      source: '1744620284607'
      sourceHandle: source
      target: '1744679036603'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables: []
      height: 52
      id: '1744610481918'
      position:
        x: 30
        y: 857
      positionAbsolute:
        x: 30
        y: 857
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        break_conditions:
        - comparison_operator: not empty
          id: 3b8519cbf4224f07b8e10544934fbac7
          value: ''
          varType: string
          variable_selector:
          - conversation
          - finallAnswer
        desc: ''
        error_handle_mode: terminated
        height: 1003
        logical_operator: and
        loop_count: 3
        selected: false
        start_node_id: 1744614444681start
        title: 循环
        type: loop
        width: 3252
      height: 1003
      id: '1744614444681'
      position:
        x: 936
        y: 857
      positionAbsolute:
        x: 936
        y: 857
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 3252
      zIndex: 1
    - data:
        desc: ''
        isInLoop: true
        selected: false
        title: ''
        type: loop-start
      draggable: false
      height: 48
      id: 1744614444681start
      parentId: '1744614444681'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 960
        y: 925
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-loop-start
      width: 44
      zIndex: 1002
    - data:
        dataset_ids:
        - 2250ed95-a817-436a-b1d0-5c04baea5cc3
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        multiple_retrieval_config:
          reranking_enable: false
          reranking_mode: reranking_model
          reranking_model:
            model: netease-youdao/bce-reranker-base_v1
            provider: langgenius/siliconflow/siliconflow
          score_threshold: null
          top_k: 5
        query_variable_selector:
        - '1744614486889'
        - result
        retrieval_mode: multiple
        selected: false
        title: 知识检索
        type: knowledge-retrieval
      height: 90
      id: '1744614478508'
      parentId: '1744614444681'
      position:
        x: 557.1956786080493
        y: 195.092842645666
      positionAbsolute:
        x: 1493.1956786080493
        y: 1052.092842645666
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        code: "\ndef main(arg1: str, arg2: str) -> dict:\n    result = arg2 if arg2\
          \ else arg1\n    return {\n        \"result\": result,\n    }\n"
        code_language: python3
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 代码 query转换
        type: code
        variables:
        - value_selector:
          - sys
          - query
          variable: arg1
        - value_selector:
          - conversation
          - reQuery
          variable: arg2
      height: 52
      id: '1744614486889'
      parentId: '1744614444681'
      position:
        x: 437.037852857689
        y: 65
      positionAbsolute:
        x: 1373.037852857689
        y: 922
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-7B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 1265245f-dbb6-48cb-975c-1e5ff0413e2f
          role: system
          text: '您是一名评分专家。您将获得一个问题和多个事实文档。您需要对问题和每个事实文档的相关性进行分别评分，分数为0-10分，十分是最高（最佳）分数。0分是您可以给出的最低分数。

            问题： {{#sys.query#}}

            多个事实文档：{{#1744615311611.result#}}

            最后，您需要根据文档数量以及质量，将评分较高的文档以及其内容挨个换行输出。没有可预定项或解释，不要说其他的话。'
        selected: false
        title: LLM 文档评分
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744615075452'
      parentId: '1744614444681'
      position:
        x: 718.0302708470128
        y: 506.3759863068642
      positionAbsolute:
        x: 1654.0302708470128
        y: 1363.3759863068642
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        code: "def main(arg: []) -> dict:\n    formatted_data = []\n    for idx, item\
          \ in enumerate(arg, 1):\n        content = item.get('content')\n       \
          \ formatted_data.append(f\"文档{idx}. {content}\")\n    \n    result_str =\
          \ \"\\n\\n\".join(formatted_data)\n    return {\"result\": result_str}\n"
        code_language: python3
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 代码 文档处理
        type: code
        variables:
        - value_selector:
          - '1744614478508'
          - result
          variable: arg
      height: 52
      id: '1744615311611'
      parentId: '1744614444681'
      position:
        x: 618.7160866191161
        y: 369.4198167119764
      positionAbsolute:
        x: 1554.7160866191161
        y: 1226.4198167119764
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-7B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: d8c1da31-81f0-4f42-bd1f-e6b423fef367
          role: system
          text: '您是一个问题重写器，可将输入问题转换为经过优化的更好版本，用于 VectorStore 检索。查看输入问题并尝试推理潜在的语义意图/含义。

            以下是需要优化的初始问题：{{#sys.query#}}

            请生成语义表达更完整、检索效果更佳的改进版本。'
        selected: false
        title: LLM 问题重写
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744616236018'
      parentId: '1744614444681'
      position:
        x: 2530.8255332965527
        y: 65
      positionAbsolute:
        x: 3466.8255332965527
        y: 922
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-72B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 99c6ac98-846e-4c21-9b18-8c290a19d527
          role: system
          text: '请根据以下检索到的文档内容和问题生成一个详细且准确的回答，如果文档内容与问题无关，请回答不知道。

            文档内容：{{#conversation.filtered_docs#}}

            问题：{{#sys.query#}}

            回答：'
        selected: false
        title: LLM 生成答案
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744616339334'
      parentId: '1744614444681'
      position:
        x: 928.2771979144438
        y: 883.4005044301496
      positionAbsolute:
        x: 1864.2771979144438
        y: 1740.4005044301496
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1744616236018'
          - text
          variable_selector:
          - conversation
          - reQuery
          write_mode: over-write
        - input_type: variable
          operation: clear
          value: ''
          variable_selector:
          - conversation
          - filtered_docs
          write_mode: over-write
        loop_id: '1744614444681'
        selected: false
        title: 变量赋值 3
        type: assigner
        version: '2'
      height: 114
      id: '1744616409947'
      parentId: '1744614444681'
      position:
        x: 2865.4586552623837
        y: 164.44181646061702
      positionAbsolute:
        x: 3801.4586552623837
        y: 1021.441816460617
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-7B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: b2579513-ca9e-4582-932d-80c7a9c677a7
          role: system
          text: '您是一名评分员，负责评估LLM的生成结果是否是以一组检索到的事实文档为基础或者得到支持。

            给出分数 ''yes'' 或 ''no''。yes 意味着生成结果以一组事实为基础或者得到支持。no 代表事实文档并不能支持该生成结果。

            yes 是最高（最佳）分数。no 是您可以给出的最低分数。

            生成结果： {{#1744616339334.text#}}

            事实文档：{{#conversation.filtered_docs#}}

            只需要回答 ''yes'' 或 ''no'' 分数，以指示文档是否能支持生成结果，没有可预定项或解释，不要说其他的话。'
        selected: false
        title: LLM 幻觉判断
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744616451373'
      parentId: '1744614444681'
      position:
        x: 1220.0261617388223
        y: 818.2946367559523
      positionAbsolute:
        x: 2156.0261617388223
        y: 1675.2946367559523
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: is not
            id: bc4bcbec-ce33-4fc0-a959-b1eec2e706c2
            value: 'yes'
            varType: string
            variable_selector:
            - '1744616451373'
            - text
          id: 'true'
          logical_operator: and
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        selected: false
        title: 条件分支 幻觉结果
        type: if-else
      height: 124
      id: '1744616501416'
      parentId: '1744614444681'
      position:
        x: 1492.2843532757056
        y: 629.729220595822
      positionAbsolute:
        x: 2428.2843532757056
        y: 1486.729220595822
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-7B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 94eadf88-b7fc-41ac-9cc0-d128905d7cb1
          role: system
          text: '您是一名评分员，负责评估LLM的生成结果是否解决了用户的问题。

            给出分数 ''yes'' 或 ''no''。yes 代表着生成结果可以解决问题。no 代表生成结果不能解决问题

            yes 是最高（最佳）分数。no 是您可以给出的最低分数。

            生成结果： {{#1744616339334.text#}}

            用户的问题：{{#sys.query#}}

            只需要回答 ''yes'' 或 ''no'' 分数，以指示文档是否能支持生成结果，没有可预定项或解释，不要说其他的话。'
        selected: false
        title: LLM 答案检验
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744616675645'
      parentId: '1744614444681'
      position:
        x: 1825.0154501131187
        y: 722.4003463607582
      positionAbsolute:
        x: 2761.0154501131187
        y: 1579.4003463607582
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: is not
            id: 4da2419a-b0de-474a-8ff9-72788fa07687
            value: 'yes'
            varType: string
            variable_selector:
            - '1744616675645'
            - text
          id: 'true'
          logical_operator: and
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        selected: false
        title: 条件分支 答案结果
        type: if-else
      height: 124
      id: '1744616720788'
      parentId: '1744614444681'
      position:
        x: 2152.6939487933687
        y: 665.8519647979847
      positionAbsolute:
        x: 3088.6939487933687
        y: 1522.8519647979847
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        author: tanchg
        desc: ''
        height: 322
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          14px;","text":"逻辑误差","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":"font-size:
          14px;"},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          14px;","text":"由于dify不能在循环内嵌套循环，导致以下误差：","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":"font-size:
          14px;"},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          14px;","text":"1.原本评分逻辑是对单个chunk进行评分，然后保留合格的chunk，如果没有合格chunk，则重写query再召回。","type":"text","version":1},{"type":"linebreak","version":1},{"detail":0,"format":0,"mode":"normal","style":"font-size:
          14px;","text":"此处改为对所有chunk进行分别评分，返回评分最高的chunk。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":"font-size:
          14px;"},{"children":[],"direction":null,"format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          14px;","text":"2.没有内部循环后，幻觉检验的内部循环生成和答案合理性检验的内部循环query重写只能在外部进行循环，会占用外部循环次数。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":"font-size:
          14px;"}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: blue
        title: ''
        type: ''
        width: 409
      height: 322
      id: '1744617116255'
      position:
        x: 403.91700023997305
        y: 1117.7684383584242
      positionAbsolute:
        x: 403.91700023997305
        y: 1117.7684383584242
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 409
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: empty
            id: 33f49369-33dd-4a00-b689-e8642a1d6c55
            value: ''
            varType: array[string]
            variable_selector:
            - conversation
            - filtered_docs
          id: 'true'
          logical_operator: and
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        selected: false
        title: 条件分支 循环生成
        type: if-else
      height: 124
      id: '1744617750070'
      parentId: '1744614444681'
      position:
        x: 131.89513582567338
        y: 65
      positionAbsolute:
        x: 1067.8951358256734
        y: 922
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1744615075452'
          - text
          variable_selector:
          - conversation
          - filtered_docs
          write_mode: over-write
        loop_id: '1744614444681'
        selected: false
        title: 变量赋值 2
        type: assigner
        version: '2'
      height: 86
      id: '1744618887928'
      parentId: '1744614444681'
      position:
        x: 863.6859775998739
        y: 695.1280618140156
      positionAbsolute:
        x: 1799.685977599874
        y: 1552.1280618140156
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        items:
        - input_type: variable
          operation: clear
          value: ''
          variable_selector:
          - conversation
          - reQuery
          write_mode: over-write
        loop_id: '1744614444681'
        selected: false
        title: 变量赋值 3
        type: assigner
        version: '2'
      height: 86
      id: '1744619138669'
      parentId: '1744614444681'
      position:
        x: 1792.394517756758
        y: 520.7816584447619
      positionAbsolute:
        x: 2728.394517756758
        y: 1377.781658444762
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1744616339334'
          - text
          variable_selector:
          - conversation
          - finallAnswer
          write_mode: over-write
        loop_id: '1744614444681'
        selected: false
        title: 变量赋值 4
        type: assigner
        version: '2'
      height: 86
      id: '1744619976993'
      parentId: '1744614444681'
      position:
        x: 2477.987726194909
        y: 764.2471324932201
      positionAbsolute:
        x: 3413.987726194909
        y: 1621.24713249322
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: empty
            id: 7cb087b3-5ac5-4421-8cf0-d61a0f7dec05
            value: ''
            varType: string
            variable_selector:
            - conversation
            - finallAnswer
          id: 'true'
          logical_operator: and
        desc: ''
        selected: false
        title: 条件分支 结果判定
        type: if-else
      height: 124
      id: '1744620253642'
      position:
        x: 4321
        y: 857
      positionAbsolute:
        x: 4321
        y: 857
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        answer: 抱歉，我不知道
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 100
      id: '1744620274008'
      position:
        x: 4623
        y: 857
      positionAbsolute:
        x: 4623
        y: 857
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        answer: '{{#conversation.finallAnswer#}}'
        desc: ''
        selected: false
        title: 直接回复 2
        type: answer
        variables: []
      height: 103
      id: '1744620284607'
      position:
        x: 4629.892186818637
        y: 1004.482863649434
      positionAbsolute:
        x: 4629.892186818637
        y: 1004.482863649434
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-7B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: df97e61e-fc61-4a40-85ad-c3dcde9c32f2
          role: system
          text: '您是一个问题路由专家。请严格根据以下规则判断：

            1. 如果问题是和公司以及企业有关的，并且向量库中可能包含相关答案，那么请回答 "vectorstore"。

            2. 否则（如问题涉及其他领域或需要实时信息）回答 "web"。

            3. 仅输出 "vectorstore" 或 "web"，无其他内容。

            用户问题：{{#sys.query#}}'
        selected: true
        title: LLM 路由
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744623005945'
      position:
        x: 332
        y: 857
      positionAbsolute:
        x: 332
        y: 857
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: is
            id: 6d1f6ad5-face-4f9f-b2a3-cdf4dcf92116
            value: vectorstore
            varType: string
            variable_selector:
            - '1744623005945'
            - text
          id: 'true'
          logical_operator: and
        desc: ''
        selected: false
        title: 条件分支 路由
        type: if-else
      height: 124
      id: '1744623102407'
      position:
        x: 634
        y: 857
      positionAbsolute:
        x: 634
        y: 857
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: The search query you want to execute with Tavily.
            ja_JP: The search query you want to execute with Tavily.
            pt_BR: The search query you want to execute with Tavily.
            zh_Hans: 您想用 Tavily 执行的搜索查询。
          label:
            en_US: Query
            ja_JP: Query
            pt_BR: Query
            zh_Hans: 查询
          llm_description: The search query.
          max: null
          min: null
          name: query
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: basic
          form: llm
          human_description:
            en_US: The depth of the search.
            ja_JP: The depth of the search.
            pt_BR: The depth of the search.
            zh_Hans: 搜索的深度。
          label:
            en_US: Search Depth
            ja_JP: Search Depth
            pt_BR: Search Depth
            zh_Hans: 搜索深度
          llm_description: The depth of the search. 'basic' for standard search, 'advanced'
            for more comprehensive results.
          max: null
          min: null
          name: search_depth
          options:
          - label:
              en_US: Basic
              ja_JP: Basic
              pt_BR: Basic
              zh_Hans: 基本
            value: basic
          - label:
              en_US: Advanced
              ja_JP: Advanced
              pt_BR: Advanced
              zh_Hans: 高级
            value: advanced
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: select
        - auto_generate: null
          default: general
          form: llm
          human_description:
            en_US: The category of the search.
            ja_JP: The category of the search.
            pt_BR: The category of the search.
            zh_Hans: 搜索的类别。
          label:
            en_US: Topic
            ja_JP: Topic
            pt_BR: Topic
            zh_Hans: 主题
          llm_description: The category of the search. Options include 'general',
            'news', or 'finance'.
          max: null
          min: null
          name: topic
          options:
          - label:
              en_US: General
              ja_JP: General
              pt_BR: General
              zh_Hans: 一般
            value: general
          - label:
              en_US: News
              ja_JP: News
              pt_BR: News
              zh_Hans: 新闻
            value: news
          - label:
              en_US: Finance
              ja_JP: Finance
              pt_BR: Finance
              zh_Hans: 金融
            value: finance
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: select
        - auto_generate: null
          default: 3
          form: llm
          human_description:
            en_US: The number of days back from the current date to include in the
              search results (only applicable when "topic" is "news").
            ja_JP: The number of days back from the current date to include in the
              search results (only applicable when "topic" is "news").
            pt_BR: The number of days back from the current date to include in the
              search results (only applicable when "topic" is "news").
            zh_Hans: 从当前日期起向前追溯的天数，以包含在搜索结果中（仅当"topic"为"news"时适用）。
          label:
            en_US: Days
            ja_JP: Days
            pt_BR: Days
            zh_Hans: 天数
          llm_description: The number of days back from the current date to include
            in the search results. Only applicable when "topic" is "news".
          max: null
          min: 1
          name: days
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: number
        - auto_generate: null
          default: not_specified
          form: llm
          human_description:
            en_US: The time range back from the current date to filter results.
            ja_JP: The time range back from the current date to filter results.
            pt_BR: The time range back from the current date to filter results.
            zh_Hans: 从当前日期起向后筛选结果的时间范围。
          label:
            en_US: Time Range
            ja_JP: Time Range
            pt_BR: Time Range
            zh_Hans: 时间范围
          llm_description: The time range back from the current date to filter results.
            Options include 'not_specified', 'day', 'week', 'month', or 'year'.
          max: null
          min: null
          name: time_range
          options:
          - label:
              en_US: Not Specified
              ja_JP: Not Specified
              pt_BR: Not Specified
              zh_Hans: 不指定
            value: not_specified
          - label:
              en_US: Day
              ja_JP: Day
              pt_BR: Day
              zh_Hans: 天
            value: day
          - label:
              en_US: Week
              ja_JP: Week
              pt_BR: Week
              zh_Hans: 周
            value: week
          - label:
              en_US: Month
              ja_JP: Month
              pt_BR: Month
              zh_Hans: 月
            value: month
          - label:
              en_US: Year
              ja_JP: Year
              pt_BR: Year
              zh_Hans: 年
            value: year
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: select
        - auto_generate: null
          default: 5
          form: form
          human_description:
            en_US: The maximum number of search results to return.
            ja_JP: The maximum number of search results to return.
            pt_BR: The maximum number of search results to return.
            zh_Hans: 要返回的最大搜索结果数。
          label:
            en_US: Max Results
            ja_JP: Max Results
            pt_BR: Max Results
            zh_Hans: 最大结果数
          llm_description: The maximum number of search results to return. Range is
            1-20.
          max: 20
          min: 1
          name: max_results
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: number
        - auto_generate: null
          default: 0
          form: form
          human_description:
            en_US: Include a list of query-related images in the response.
            ja_JP: Include a list of query-related images in the response.
            pt_BR: Include a list of query-related images in the response.
            zh_Hans: 在响应中包含与查询相关的图片列表。
          label:
            en_US: Include Images
            ja_JP: Include Images
            pt_BR: Include Images
            zh_Hans: 包含图片
          llm_description: When set to true, includes a list of query-related images
            in the response.
          max: null
          min: null
          name: include_images
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: boolean
        - auto_generate: null
          default: 0
          form: form
          human_description:
            en_US: When include_images is True, adds descriptive text for each image.
            ja_JP: When include_images is True, adds descriptive text for each image.
            pt_BR: When include_images is True, adds descriptive text for each image.
            zh_Hans: 当 include_images 为 True 时，为每个图像添加描述文本。
          label:
            en_US: Include Image Descriptions
            ja_JP: Include Image Descriptions
            pt_BR: Include Image Descriptions
            zh_Hans: 包含图片描述
          llm_description: When include_images is True and this is set to true, adds
            descriptive text for each image.
          max: null
          min: null
          name: include_image_descriptions
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: boolean
        - auto_generate: null
          default: 0
          form: form
          human_description:
            en_US: Include a short answer to the original query in the response.
            ja_JP: Include a short answer to the original query in the response.
            pt_BR: Include a short answer to the original query in the response.
            zh_Hans: 在响应中包含对原始查询的简短回答。
          label:
            en_US: Include Answer
            ja_JP: Include Answer
            pt_BR: Include Answer
            zh_Hans: 包含答案
          llm_description: When set to true, includes a short answer to the original
            query in the response.
          max: null
          min: null
          name: include_answer
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: boolean
        - auto_generate: null
          default: 0
          form: form
          human_description:
            en_US: Include the cleaned and parsed HTML content of each search result.
            ja_JP: Include the cleaned and parsed HTML content of each search result.
            pt_BR: Include the cleaned and parsed HTML content of each search result.
            zh_Hans: 包含每个搜索结果的已清理和解析的HTML内容。
          label:
            en_US: Include Raw Content
            ja_JP: Include Raw Content
            pt_BR: Include Raw Content
            zh_Hans: 包含原始内容
          llm_description: When set to true, includes the cleaned and parsed HTML
            content of each search result.
          max: null
          min: null
          name: include_raw_content
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: boolean
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: A comma-separated list of domains to specifically include in the
              search results.
            ja_JP: A comma-separated list of domains to specifically include in the
              search results.
            pt_BR: A comma-separated list of domains to specifically include in the
              search results.
            zh_Hans: 要在搜索结果中特别包含的域的逗号分隔列表。
          label:
            en_US: Include Domains
            ja_JP: Include Domains
            pt_BR: Include Domains
            zh_Hans: 包含域
          llm_description: A comma-separated list of domains to specifically include
            in the search results.
          max: null
          min: null
          name: include_domains
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: A comma-separated list of domains to specifically exclude from
              the search results.
            ja_JP: A comma-separated list of domains to specifically exclude from
              the search results.
            pt_BR: A comma-separated list of domains to specifically exclude from
              the search results.
            zh_Hans: 要从搜索结果中特别排除的域的逗号分隔列表。
          label:
            en_US: Exclude Domains
            ja_JP: Exclude Domains
            pt_BR: Exclude Domains
            zh_Hans: 排除域
          llm_description: A comma-separated list of domains to specifically exclude
            from the search results.
          max: null
          min: null
          name: exclude_domains
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: string
        params:
          days: ''
          exclude_domains: ''
          include_answer: ''
          include_domains: ''
          include_image_descriptions: ''
          include_images: ''
          include_raw_content: ''
          max_results: ''
          query: ''
          search_depth: ''
          time_range: ''
          topic: ''
        provider_id: langgenius/tavily/tavily
        provider_name: langgenius/tavily/tavily
        provider_type: builtin
        selected: false
        title: Tavily Search
        tool_configurations:
          exclude_domains: null
          include_answer: 0
          include_domains: null
          include_image_descriptions: 0
          include_images: 0
          include_raw_content: 0
          max_results: 5
        tool_label: Tavily Search
        tool_name: tavily_search
        tool_parameters:
          query:
            type: mixed
            value: '{{#sys.query#}}'
        type: tool
      height: 244
      id: '1744623172505'
      position:
        x: 875.5272016985498
        y: 2008.5447091276046
      positionAbsolute:
        x: 875.5272016985498
        y: 2008.5447091276046
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-72B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 99c6ac98-846e-4c21-9b18-8c290a19d527
          role: system
          text: '请根据以下检索到的文档内容和问题生成一个详细且准确的回答，如果文档内容与问题无关，请回答不知道。

            文档内容：{{#1744623172505.text#}}

            问题：{{#sys.query#}}

            回答：'
        selected: false
        title: LLM 生成答案 (web)
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '17446232505850'
      position:
        x: 1298.0952059449228
        y: 2026.6127133739783
      positionAbsolute:
        x: 1298.0952059449228
        y: 2026.6127133739783
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        answer: '{{#17446232505850.text#}}'
        desc: ''
        selected: false
        title: 直接回复 web
        type: answer
        variables: []
      height: 103
      id: '1744623298915'
      position:
        x: 1688.809588110154
        y: 2067.7759235652748
      positionAbsolute:
        x: 1688.809588110154
        y: 2067.7759235652748
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: clear
          value: ''
          variable_selector:
          - conversation
          - finallAnswer
          write_mode: over-write
        - input_type: variable
          operation: clear
          value: ''
          variable_selector:
          - conversation
          - filtered_docs
          write_mode: over-write
        - input_type: variable
          operation: clear
          value: ''
          variable_selector:
          - conversation
          - reQuery
          write_mode: over-write
        selected: false
        title: 变量赋值 5
        type: assigner
        version: '2'
      height: 142
      id: '1744679036603'
      position:
        x: 5029.047498646755
        y: 915.601464755069
      positionAbsolute:
        x: 5029.047498646755
        y: 915.601464755069
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        author: tanchg
        desc: ''
        height: 146
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"Adaptive RAG：在self RAG的基础上增加问题路由，非知识库相关问题，路由到web搜索。如果切换数据库，需要更换LLM
          路由的提示词。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":"font-size:
          16px;"},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"比如：当前数据为企业规则制度，那么提示词中需要提示LLM","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":"font-size:
          16px;"}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: blue
        title: ''
        type: ''
        width: 392
      height: 146
      id: '1744698718738'
      position:
        x: 634
        y: 661.702962430533
      positionAbsolute:
        x: 634
        y: 661.702962430533
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 392
    viewport:
      x: -239.0324896696991
      y: -434.0091656068578
      zoom: 1.0000000367242603
