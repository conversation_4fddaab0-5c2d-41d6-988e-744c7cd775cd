kind: app
version: "0.1.5"
app:
  mode: workflow
  name: "简易Dify工作流生成器"
  description: "根据场景描述自动生成Dify工作流DSL"
  icon: "🔄"
  icon_background: "#E6F7FF"
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: package
  value:
    plugin_unique_identifier: langgenius/openai_api_compatible:0.1.11@aeb88e81bb79db614de257ad66eddde90cede8a3917c986fbd2be46df84d2e23
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ""
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ""
      voice: ""
  graph:
    edges:
    - data:
        isInIteration: false
        sourceType: start
        targetType: llm
      id: start_node-source-analyze_scenario-target
      source: start_node
      sourceHandle: source
      target: analyze_scenario
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: llm
      id: analyze_scenario-source-generate_dsl-target
      source: analyze_scenario
      sourceHandle: source
      target: generate_dsl
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: end
      id: generate_dsl-source-end_node-target
      source: generate_dsl
      sourceHandle: source
      target: end_node
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        availableNextNodes: []
        availablePrevNodes: []
        selected: true
        title: "开始节点"
        type: start
        variables:
        - label: "场景描述"
          max_length: 2000
          required: true
          type: paragraph
          variable: scenario_description
        vision:
          enabled: false
      height: 90
      id: start_node
      position:
        x: 80
        y: 282
      positionAbsolute:
        x: 80
        y: 282
      selected: true
      type: custom
      width: 244

    - data:
        availableNextNodes: []
        availablePrevNodes: []
        context:
          enabled: true
          variable_selector:
          - start_node
          - scenario_description
        model:
          completion_params:
            temperature: 0.5
          mode: chat
          name: glm-4-flashx
          provider: langgenius/openai_api_compatible/openai_api_compatible
        outputs:
          text:
            type: string
        prompt_template:
        - id: system-prompt
          role: system
          text: |
            你是一个专业的Dify工作流分析专家。你的任务是分析用户提供的场景描述，提取关键信息，为后续生成Dify工作流DSL做准备。

            请分析用户提供的场景描述，提取以下信息：
            1. 工作流名称和描述
            2. 输入内容和类型
            3. 输出内容和类型
            4. 流程步骤及其对应的节点类型
            5. 节点之间的连接关系

            请以文本形式返回分析结果，包含以下部分：

            工作流名称：xxx
            工作流描述：xxx

            输入：
            - 输入1：类型
            - 输入2：类型

            输出：
            - 输出1：类型
            - 输出2：类型

            流程步骤：
            1. 步骤1（节点类型）：描述
            2. 步骤2（节点类型）：描述
            ...

            连接关系：
            - 步骤1 -> 步骤2
            - 步骤2 -> 步骤3
            ...

            注意：
            - 节点类型应该是Dify支持的类型：start, llm, code, if-else, question-classifier, tool, knowledge等
            - 确保流程步骤的顺序合理
            - 确保连接关系正确反映流程步骤的顺序
        - id: human-prompt
          role: user
          text: |
            请分析以下场景描述：

            {{scenario_description}}
        retriever_resource:
          enabled: true
          retriever_resource_selector:
          - DSL知识库
        selected: false
        title: "分析场景描述"
        type: llm
        vision:
          enabled: false
      height: 90
      id: analyze_scenario
      position:
        x: 380
        y: 282
      positionAbsolute:
        x: 380
        y: 282
      selected: false
      type: custom
      width: 244

    - data:
        availableNextNodes: []
        availablePrevNodes: []
        context:
          enabled: true
          variable_selector:
          - analyze_scenario
          - text
          - start_node
          - scenario_description
        model:
          completion_params:
            temperature: 0.5
          mode: chat
          name: glm-4-flashx
          provider: langgenius/openai_api_compatible/openai_api_compatible
        outputs:
          text:
            type: string
        prompt_template:
        - id: system-prompt
          role: system
          text: |
            你是一个专业的Dify工作流DSL生成专家。你的任务是根据场景分析结果，生成完整的Dify工作流DSL文件内容。

            请注意以下重要事项：
            1. DSL文件必须以kind: app、version: "0.1.5"和app配置开头
            2. 必须将app.mode设置为"workflow"
            3. 为所有节点添加必要的属性，如variables和outputs
            4. 确保节点类型名称一致，特别是在sourceType和targetType中
            5. 当varType为string时，确保相应的value也是字符串而不是数组
            6. 使用code_language: python3而非language: python
            7. 正确指定数组类型：array[object]、array[string]或array[number]
            8. 对需要访问其他节点变量的节点，启用上下文功能

            请生成完整的YAML格式DSL文件，确保格式正确，可以直接在Dify平台上使用。

            DSL文件必须包含以下结构：
            ```yaml
            kind: app
            version: "0.1.5"
            app:
              mode: workflow
              name: "工作流名称"
              description: "工作流描述"
              icon: "图标"
              icon_background: "#颜色代码"
              use_icon_as_answer_icon: false
            dependencies:
            - current_identifier: null
              type: package
              value:
                plugin_unique_identifier: langgenius/openai_api_compatible:0.1.11@aeb88e81bb79db614de257ad66eddde90cede8a3917c986fbd2be46df84d2e23
            workflow:
              conversation_variables: []
              environment_variables: []
              features:
                # 特性配置...
              graph:
                edges:
                  # 边定义...
                nodes:
                  # 节点定义...
                viewport:
                  x: 0
                  y: 0
                  zoom: 1
            ```

            最后，请以以下格式回复用户：

            ## 🎉 工作流生成成功

            已为您生成工作流的DSL文件。

            ### 📝 使用说明

            1. 复制下方的YAML代码
            2. 在Dify平台创建新的工作流应用
            3. 选择"导入DSL"选项
            4. 粘贴YAML代码并导入

            ### 📄 DSL文件内容

            ```yaml
            (这里是生成的DSL内容)
            ```

            ### ⚠️ 注意事项

            - 导入后请检查工作流配置，确保所有节点和连接正确
            - 可能需要根据实际需求调整LLM节点的提示词
            - 对于代码节点，可能需要完善代码逻辑

            如需修改或优化工作流，请提供更详细的需求描述。
        - id: human-prompt
          role: user
          text: |
            请根据以下场景分析和原始描述生成完整的Dify工作流DSL：

            场景分析：
            {{text}}

            原始描述：
            {{scenario_description}}
        retriever_resource:
          enabled: true
          retriever_resource_selector:
          - DSL知识库
        selected: false
        title: "生成DSL"
        type: llm
        vision:
          enabled: false
      height: 90
      id: generate_dsl
      position:
        x: 680
        y: 282
      positionAbsolute:
        x: 680
        y: 282
      selected: false
      type: custom
      width: 244

    - data:
        availableNextNodes: []
        availablePrevNodes: []
        outputs:
        - value_selector:
          - generate_dsl
          - text
          variable: workflow_dsl
        selected: false
        title: "结束"
        type: end
        vision:
          enabled: false
      height: 90
      id: end_node
      position:
        x: 980
        y: 282
      positionAbsolute:
        x: 980
        y: 282
      selected: false
      type: custom
      width: 244
    viewport:
      x: 0
      y: 0
      zoom: 1
