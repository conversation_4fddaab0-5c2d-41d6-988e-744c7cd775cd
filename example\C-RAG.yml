app:
  description: Corrective RAG：CRAG通过系统地识别和纠正生成响应中的错误，利用迭代提炼和反馈机制，可以提高LLM输出的整体精度和可靠性
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: 【测试】C-RAG
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/moonshot:0.0.4@684e36cde2a78f7edd6faa0d07e530d8042115677b0d42acfc07fce9407d52e9
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/siliconflow:0.0.8@217f973bd7ced1b099c2f0c669f1356bdf4cc38b8372fd58d7874f9940b95de3
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/zhipuai:0.0.7@1ee8fe156cc3dffcd085d7fc5581395aecf667cfb548c8d621e505b8a160b619
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: knowledge-retrieval
      id: 1743563144594-source-1743563174801-target
      selected: false
      source: '1743563144594'
      sourceHandle: source
      target: '1743563174801'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: knowledge-retrieval
        targetType: iteration
      id: 1743563174801-source-1743563746565-target
      selected: false
      source: '1743563174801'
      sourceHandle: source
      target: '1743563746565'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: true
        isInLoop: false
        iteration_id: '1743563746565'
        sourceType: iteration-start
        targetType: llm
      id: 1743563746565start-source-1743563771217-target
      selected: false
      source: 1743563746565start
      sourceHandle: source
      target: '1743563771217'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: iteration
        targetType: code
      id: 1743563746565-source-1743563884105-target
      selected: false
      source: '1743563746565'
      sourceHandle: source
      target: '1743563884105'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: code
        targetType: if-else
      id: 1743563884105-source-1743564215575-target
      selected: false
      source: '1743563884105'
      sourceHandle: source
      target: '1743564215575'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 1743564215575-false-1743564386046-target
      selected: false
      source: '1743564215575'
      sourceHandle: 'false'
      target: '1743564386046'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: tool
      id: 1743564386046-source-1743564583078-target
      selected: false
      source: '1743564386046'
      sourceHandle: source
      target: '1743564583078'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: tool
        targetType: template-transform
      id: 1743564583078-source-1743564736927-target
      source: '1743564583078'
      sourceHandle: source
      target: '1743564736927'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: template-transform
        targetType: variable-aggregator
      id: 1743564736927-source-1743564801945-target
      source: '1743564736927'
      sourceHandle: source
      target: '1743564801945'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: if-else
        targetType: template-transform
      id: 1743564215575-true-1743564846242-target
      source: '1743564215575'
      sourceHandle: 'true'
      target: '1743564846242'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: template-transform
        targetType: variable-aggregator
      id: 1743564846242-source-1743564801945-target
      source: '1743564846242'
      sourceHandle: source
      target: '1743564801945'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: variable-aggregator
        targetType: llm
      id: 1743564801945-source-1743563197118-target
      source: '1743564801945'
      sourceHandle: source
      target: '1743563197118'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: end
      id: 1743563197118-source-1744697913533-target
      source: '1743563197118'
      sourceHandle: source
      target: '1744697913533'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 问题
          max_length: 1000
          options: []
          required: true
          type: paragraph
          variable: query
      height: 88
      id: '1743563144594'
      position:
        x: -12.23739245372434
        y: -23.779970750407116
      positionAbsolute:
        x: -12.23739245372434
        y: -23.779970750407116
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        dataset_ids:
        - 2250ed95-a817-436a-b1d0-5c04baea5cc3
        desc: ''
        multiple_retrieval_config:
          reranking_enable: false
          reranking_mode: reranking_model
          reranking_model:
            model: netease-youdao/bce-reranker-base_v1
            provider: langgenius/siliconflow/siliconflow
          top_k: 4
        query_variable_selector:
        - '1743563144594'
        - query
        retrieval_mode: multiple
        selected: false
        title: 知识检索
        type: knowledge-retrieval
      height: 90
      id: '1743563174801'
      position:
        x: 389.8973455696007
        y: -23.779970750407116
      positionAbsolute:
        x: 389.8973455696007
        y: -23.779970750407116
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: 最终输出
        model:
          completion_params: {}
          mode: chat
          name: moonshot-v1-8k
          provider: langgenius/moonshot/moonshot
        prompt_template:
        - id: 4def6899-fa98-4cb2-9761-54bfbdfa8a5c
          role: system
          text: '以下是上下文

            -------------------------

            {{#1743564801945.output#}}

            -------------------------

            请根据上面的上下文，回答以下问题，不要编造其他内容。

            如果上下文中不存在相关信息，请拒绝回答。

            问题：{{#1743563144594.query#}}

            答案：

            '
        selected: false
        title: LLM
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1743563197118'
      position:
        x: 1511.5075017263302
        y: 533.5097204492881
      positionAbsolute:
        x: 1511.5075017263302
        y: 533.5097204492881
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        desc: 迭代评估检索出的文本
        error_handle_mode: terminated
        height: 203
        is_parallel: false
        iterator_selector:
        - '1743563174801'
        - result
        output_selector:
        - '1743563771217'
        - text
        output_type: array[string]
        parallel_nums: 10
        selected: false
        start_node_id: 1743563746565start
        title: 迭代
        type: iteration
        width: 388
      height: 203
      id: '1743563746565'
      position:
        x: -17.52021450286361
        y: 151.78962333130048
      positionAbsolute:
        x: -17.52021450286361
        y: 151.78962333130048
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 388
      zIndex: 1
    - data:
        desc: ''
        isInIteration: true
        selected: false
        title: ''
        type: iteration-start
      draggable: false
      height: 48
      id: 1743563746565start
      parentId: '1743563746565'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 6.47978549713639
        y: 219.78962333130048
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-iteration-start
      width: 44
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: 文本相关性评估
        isInIteration: true
        isInLoop: false
        iteration_id: '1743563746565'
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: glm-4-plus
          provider: langgenius/zhipuai/zhipuai
        prompt_template:
        - id: d3a7b7bd-0625-4a88-bb39-09bbe6a33baa
          role: system
          text: '你是一个评分人员，评估检索出的文档与用户问题的相关性

            以下是检索出的文档：

            -------------------------

            {{#1743563746565.item#}}

            -------------------------


            以下是用户的问题：

            -------------------------

            {{#1743563144594.query#}}

            -------------------------


            如果文档中包含与用户问题相关的关键词或语义，且有助于解答用户问题，请将其评为相关。

            请给出 "true" 或者 "false" 来表明文档是否与问题相关。

            注意你只能输出 true 或者 false，不要有过多的解释'
        selected: false
        title: LLM 3
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1743563771217'
      parentId: '1743563746565'
      position:
        x: 115.58211002665576
        y: 65
      positionAbsolute:
        x: 98.06189552379215
        y: 216.78962333130048
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        code: "\ndef main(arg1: list, arg2: list) -> dict:\n    result = [arg1[i]\
          \ for i in range(len(arg1)) if arg2[i] == \"true\"]\n    return {\n    \
          \    \"result\": result,\n        \"is_all\": str(len(result) == len(arg1))\n\
          \    }\n"
        code_language: python3
        desc: 知识筛选
        outputs:
          is_all:
            children: null
            type: string
          result:
            children: null
            type: array[object]
        selected: false
        title: 代码执行
        type: code
        variables:
        - value_selector:
          - '1743563174801'
          - result
          variable: arg1
        - value_selector:
          - '1743563746565'
          - output
          variable: arg2
      height: 80
      id: '1743563884105'
      position:
        x: 541.8375569226439
        y: 151.78962333130048
      positionAbsolute:
        x: 541.8375569226439
        y: 151.78962333130048
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: is
            id: f72fd5fc-19a6-4f0f-9bdc-ea2b294232be
            value: 'True'
            varType: string
            variable_selector:
            - '1743563884105'
            - is_all
          id: 'true'
          logical_operator: and
        desc: 判断是否需要额外搜索
        selected: false
        title: 条件分支
        type: if-else
      height: 152
      id: '1743564215575'
      position:
        x: -36.35772428576405
        y: 503.17416795015697
      positionAbsolute:
        x: -36.35772428576405
        y: 503.17416795015697
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: 问题优化
        model:
          completion_params: {}
          mode: chat
          name: moonshot-v1-8k
          provider: langgenius/moonshot/moonshot
        prompt_template:
        - id: 55c5f77a-137b-4081-9d52-bdbb560132a2
          role: system
          text: '你需要生成对检索进行优化的问题，请根据输入内容，尝试推理其中的语义意图/含义。

            这是初始问题：

            -------------------------

            {{#1743563144594.query#}}

            -------------------------

            请提出一个改进的问题：'
        selected: false
        title: LLM 3
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1743564386046'
      position:
        x: 268.8206196016756
        y: 690.300293485484
      positionAbsolute:
        x: 268.8206196016756
        y: 690.300293485484
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        desc: 外部知识源获取
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: used for linking to webpages
            ja_JP: used for linking to webpages
            pt_BR: used for linking to webpages
            zh_Hans: 用于链接到网页
          label:
            en_US: URL
            ja_JP: URL
            pt_BR: URL
            zh_Hans: 网页链接
          llm_description: url for scraping
          max: null
          min: null
          name: url
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
            like Gecko) Chrome/100.0.1000.0 Safari/537.36
          form: form
          human_description:
            en_US: used for identifying the browser.
            ja_JP: used for identifying the browser.
            pt_BR: used for identifying the browser.
            zh_Hans: 用于识别浏览器。
          label:
            en_US: User Agent
            ja_JP: User Agent
            pt_BR: User Agent
            zh_Hans: User Agent
          llm_description: null
          max: null
          min: null
          name: user_agent
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: 'false'
          form: form
          human_description:
            en_US: If true, the crawler will only return the page summary content.
            ja_JP: If true, the crawler will only return the page summary content.
            pt_BR: If true, the crawler will only return the page summary content.
            zh_Hans: 如果启用，爬虫将仅返回页面摘要内容。
          label:
            en_US: Whether to generate summary
            ja_JP: Whether to generate summary
            pt_BR: Whether to generate summary
            zh_Hans: 是否生成摘要
          llm_description: null
          max: null
          min: null
          name: generate_summary
          options:
          - label:
              en_US: 'Yes'
              ja_JP: 'Yes'
              pt_BR: 'Yes'
              zh_Hans: 是
            value: 'true'
          - label:
              en_US: 'No'
              ja_JP: 'No'
              pt_BR: 'No'
              zh_Hans: 否
            value: 'false'
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: boolean
        params:
          generate_summary: ''
          url: ''
          user_agent: ''
        provider_id: webscraper
        provider_name: webscraper
        provider_type: builtin
        selected: false
        title: 网页爬虫
        tool_configurations:
          generate_summary: null
          user_agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
            (KHTML, like Gecko) Chrome/100.0.1000.0 Safari/537.36
        tool_label: 网页爬虫
        tool_name: webscraper
        tool_parameters:
          url:
            type: mixed
            value: '{{#1743564386046.text#}}'
        type: tool
      height: 142
      id: '1743564583078'
      position:
        x: 572.6177373717866
        y: 690.300293485484
      positionAbsolute:
        x: 572.6177373717866
        y: 690.300293485484
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        desc: 将筛选的知识和外部知识整合
        selected: false
        template: '{{ arg1 }}

          {{ arg2 }}'
        title: 模板转换
        type: template-transform
        variables:
        - value_selector:
          - '1743563746565'
          - output
          variable: arg1
        - value_selector:
          - '1743564583078'
          - text
          variable: arg2
      height: 80
      id: '1743564736927'
      position:
        x: 910.030458370815
        y: 690.300293485484
      positionAbsolute:
        x: 910.030458370815
        y: 690.300293485484
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        desc: 分支合并
        output_type: string
        selected: false
        title: 变量聚合器
        type: variable-aggregator
        variables:
        - - '1743564736927'
          - output
      height: 135
      id: '1743564801945'
      position:
        x: 1233.9909685945559
        y: 533.5097204492881
      positionAbsolute:
        x: 1233.9909685945559
        y: 533.5097204492881
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        desc: 将知识检索的输出处理成文本格式
        selected: false
        template: '{{ arg1 }}'
        title: 模板转换 2
        type: template-transform
        variables:
        - value_selector:
          - '1743563884105'
          - result
          variable: arg1
      height: 80
      id: '1743564846242'
      position:
        x: 454.64469794782997
        y: 533.5097204492881
      positionAbsolute:
        x: 454.64469794782997
        y: 533.5097204492881
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        author: tanchg
        desc: ''
        height: 149
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"Corrective RAG：通过一个轻量级评估器来评估检索到的文档的质量和相关性。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":"font-size:
          16px;"},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"然后识别和纠正生成中的错误，利用迭代提炼和反馈机制，提高LLM输出的整体精度和可靠性。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":"font-size:
          16px;"}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: blue
        title: ''
        type: ''
        width: 616
      height: 149
      id: '1744697824753'
      position:
        x: 902.4338529406157
        y: 113.97787856143168
      positionAbsolute:
        x: 902.4338529406157
        y: 113.97787856143168
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 616
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1743563197118'
          - text
          variable: result
        selected: false
        title: 结束
        type: end
      height: 88
      id: '1744697913533'
      position:
        x: 1813.5075017263302
        y: 533.5097204492881
      positionAbsolute:
        x: 1813.5075017263302
        y: 533.5097204492881
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    viewport:
      x: 62.21453518020314
      y: 133.25280542830512
      zoom: 0.921355509421651
