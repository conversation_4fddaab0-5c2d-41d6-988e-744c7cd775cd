app:
  description: 假设性文档查询转换：基于用户问题生成假设性答案再进行召回，并作为上下文回答用户问题。
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: 【测试】Hyde-RAG
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/siliconflow:0.0.8@217f973bd7ced1b099c2f0c669f1356bdf4cc38b8372fd58d7874f9940b95de3
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1744182759966-source-1744182916650-target
      source: '1744182759966'
      sourceHandle: source
      target: '1744182916650'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: knowledge-retrieval
      id: 1744182916650-source-1744183351791-target
      source: '1744182916650'
      sourceHandle: source
      target: '1744183351791'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: knowledge-retrieval
        targetType: llm
      id: 1744183351791-source-1744183381184-target
      source: '1744183351791'
      sourceHandle: source
      target: '1744183381184'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: end
      id: 1744183381184-source-1744184326874-target
      source: '1744183381184'
      sourceHandle: source
      target: '1744184326874'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: query
          max_length: 48
          options: []
          required: true
          type: text-input
          variable: query
      height: 88
      id: '1744182759966'
      position:
        x: 80
        y: 281
      positionAbsolute:
        x: 80
        y: 281
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-7B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 0adc35cb-5690-4bc5-abca-dd04ea4cda41
          role: system
          text: 你是一个问答专家，请根据以下问题生成一个假设性的回答。问题：{{#1744182759966.query#}}。回答格式：根据《XXX》第YY部分/章节/条款，...
        selected: false
        title: LLM
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744182916650'
      position:
        x: 382
        y: 281
      positionAbsolute:
        x: 382
        y: 281
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        dataset_ids:
        - 2250ed95-a817-436a-b1d0-5c04baea5cc3
        desc: ''
        multiple_retrieval_config:
          reranking_enable: true
          reranking_mode: reranking_model
          reranking_model:
            model: netease-youdao/bce-reranker-base_v1
            provider: langgenius/siliconflow/siliconflow
          top_k: 4
        query_variable_selector:
        - '1744182916650'
        - text
        retrieval_mode: multiple
        selected: false
        title: 知识检索
        type: knowledge-retrieval
      height: 90
      id: '1744183351791'
      position:
        x: 684
        y: 281
      positionAbsolute:
        x: 684
        y: 281
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        context:
          enabled: true
          variable_selector:
          - '1744183351791'
          - result
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-72B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 042b208c-4fae-4c02-b70c-d8e01a29df22
          role: system
          text: '请根据以下检索到的文档内容和问题生成一个详细且准确的回答，如果文档内容与问题无关，请回答不知道。

            文档内容：{{#context#}}

            问题：{{#1744182759966.query#}}

            回答：'
        selected: false
        title: LLM 2
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744183381184'
      position:
        x: 986.9784150606715
        y: 281
      positionAbsolute:
        x: 986.9784150606715
        y: 281
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1744183381184'
          - text
          variable: answer
        selected: false
        title: 结束
        type: end
      height: 88
      id: '1744184326874'
      position:
        x: 1288
        y: 281
      positionAbsolute:
        x: 1288
        y: 281
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        author: tanchg
        desc: ''
        height: 104
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"假设性文档查询转换模式：基于用户问题生成假设性答案再进行召回，并作为上下文回答用户问题。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: blue
        title: ''
        type: ''
        width: 739
      height: 104
      id: '1744698090374'
      position:
        x: 107.01678461369404
        y: 436.4684420223925
      positionAbsolute:
        x: 107.01678461369404
        y: 436.4684420223925
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 739
    viewport:
      x: 115.20125804660603
      y: 87.73966071360724
      zoom: 0.9353770477200275
