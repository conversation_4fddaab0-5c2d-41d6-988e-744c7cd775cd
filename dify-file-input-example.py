"""
Dify工作流中处理文件输入的示例代码
这个代码可以在Dify工作流的代码执行节点中使用
"""

def main(sys_files) -> dict:
    """
    处理文件输入的主函数
    
    Args:
        sys_files: 文件数组，通过sys.files传入
        
    Returns:
        包含处理结果的字典
    """
    # 检查是否有文件
    if not sys_files or len(sys_files) == 0:
        return {"error": "没有提供文件"}
    
    # 获取第一个文件
    file = sys_files[0]
    
    # 文件信息
    file_name = file.get("name", "未知文件名")
    file_type = file.get("type", "未知类型")
    file_url = file.get("url", "")
    
    # 根据文件类型进行不同处理
    if file_type == "xlsx" or file_type == "xls" or "excel" in file_type.lower():
        result = process_excel_file(file)
    elif file_type == "docx" or file_type == "doc" or "word" in file_type.lower():
        result = process_word_file(file)
    elif file_type == "pdf" or "pdf" in file_type.lower():
        result = process_pdf_file(file)
    else:
        result = {"message": f"未知文件类型: {file_type}"}
    
    # 返回处理结果
    return {
        "result": f"成功处理文件: {file_name}",
        "file_info": {
            "name": file_name,
            "type": file_type,
            "url": file_url
        },
        "processing_result": result
    }

def process_excel_file(file):
    """
    处理Excel文件
    
    Args:
        file: 文件对象
        
    Returns:
        处理结果
    """
    # 这里是示例代码，实际处理逻辑需要根据需求实现
    return {
        "sheet_count": 3,  # 示例数据
        "row_count": 100,  # 示例数据
        "summary": "这是一个Excel文件，包含财务数据"  # 示例数据
    }

def process_word_file(file):
    """
    处理Word文件
    
    Args:
        file: 文件对象
        
    Returns:
        处理结果
    """
    # 这里是示例代码，实际处理逻辑需要根据需求实现
    return {
        "page_count": 5,  # 示例数据
        "word_count": 1000,  # 示例数据
        "summary": "这是一个Word文档，包含项目报告"  # 示例数据
    }

def process_pdf_file(file):
    """
    处理PDF文件
    
    Args:
        file: 文件对象
        
    Returns:
        处理结果
    """
    # 这里是示例代码，实际处理逻辑需要根据需求实现
    return {
        "page_count": 10,  # 示例数据
        "is_scanned": False,  # 示例数据
        "summary": "这是一个PDF文档，包含合同内容"  # 示例数据
    }
