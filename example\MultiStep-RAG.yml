app:
  description: 多步查询转换：通过循环生成单个子问题并回答，将用户问题、上一个子问题以及对应的答案作为下一个子问题的上下文，循环结束回答原始问题。
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: 【测试】MultiStep-RAG
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/siliconflow:0.0.8@217f973bd7ced1b099c2f0c669f1356bdf4cc38b8372fd58d7874f9940b95de3
kind: app
version: 0.1.5
workflow:
  conversation_variables:
  - description: 最后一次循环的检索结果
    id: 054cc7fc-a78a-4340-8a82-9ec10dbe3f3e
    name: finalResult
    selector:
    - conversation
    - finalResult
    value: []
    value_type: array[object]
  - description: 上一个推理的问题的答案
    id: 4fdc08a8-7ea3-4fba-8b76-ac00cd2bd86a
    name: preQueryAnswer
    selector:
    - conversation
    - preQueryAnswer
    value: ''
    value_type: string
  - description: 上一个推理的问题
    id: af2f6a3f-cd39-472e-827d-a2a17eeba101
    name: preQuery
    selector:
    - conversation
    - preQuery
    value: ''
    value_type: string
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744593592934'
        sourceType: knowledge-retrieval
        targetType: llm
      id: 1744593758714-source-1744593775987-target
      selected: false
      source: '1744593758714'
      sourceHandle: source
      target: '1744593775987'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: assigner
      id: 1744595369084-source-1744597368675-target
      source: '1744595369084'
      sourceHandle: source
      target: '1744597368675'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: assigner
        targetType: answer
      id: 1744597368675-source-answer-target
      source: '1744597368675'
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744593592934'
        sourceType: loop-start
        targetType: llm
      id: 1744593592934start-source-1744598867498-target
      source: 1744593592934start
      sourceHandle: source
      target: '1744598867498'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInLoop: false
        sourceType: start
        targetType: loop
      id: 1744593277938-source-1744593592934-target
      source: '1744593277938'
      sourceHandle: source
      target: '1744593592934'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744593592934'
        sourceType: llm
        targetType: knowledge-retrieval
      id: 1744598867498-source-1744593758714-target
      source: '1744598867498'
      sourceHandle: source
      target: '1744593758714'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744593592934'
        sourceType: llm
        targetType: assigner
      id: 1744593775987-source-1744599637553-target
      source: '1744593775987'
      sourceHandle: source
      target: '1744599637553'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInLoop: false
        sourceType: loop
        targetType: llm
      id: 1744593592934-source-1744595369084-target
      source: '1744593592934'
      sourceHandle: source
      target: '1744595369084'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables: []
      height: 52
      id: '1744593277938'
      position:
        x: 30
        y: 356
      positionAbsolute:
        x: 30
        y: 356
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        answer: '{{#1744595369084.text#}}'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 103
      id: answer
      position:
        x: 2344
        y: 356
      positionAbsolute:
        x: 2344
        y: 356
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        break_conditions: []
        desc: ''
        error_handle_mode: terminated
        height: 312
        logical_operator: and
        loop_count: 3
        selected: false
        start_node_id: 1744593592934start
        title: 递归生成子问题并回答
        type: loop
        width: 1348
      height: 312
      id: '1744593592934'
      position:
        x: 332
        y: 356
      positionAbsolute:
        x: 332
        y: 356
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 1348
      zIndex: 1
    - data:
        desc: ''
        isInLoop: true
        selected: false
        title: ''
        type: loop-start
      draggable: false
      height: 48
      id: 1744593592934start
      parentId: '1744593592934'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 356
        y: 424
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-loop-start
      width: 44
      zIndex: 1002
    - data:
        dataset_ids:
        - 2250ed95-a817-436a-b1d0-5c04baea5cc3
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744593592934'
        multiple_retrieval_config:
          reranking_enable: false
          reranking_mode: reranking_model
          reranking_model:
            model: netease-youdao/bce-reranker-base_v1
            provider: langgenius/siliconflow/siliconflow
          top_k: 4
        query_variable_selector:
        - '1744598867498'
        - text
        retrieval_mode: multiple
        selected: false
        title: 知识检索
        type: knowledge-retrieval
      height: 90
      id: '1744593758714'
      parentId: '1744593592934'
      position:
        x: 442.8571428571431
        y: 66.57142857142856
      positionAbsolute:
        x: 774.8571428571431
        y: 422.57142857142856
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        context:
          enabled: true
          variable_selector:
          - '1744593758714'
          - result
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744593592934'
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-7B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 71d49d8e-c780-4559-8f00-cf4a575ea43d
          role: system
          text: '请根据以下检索到的文档内容和用户提出的问题生成一个详细且准确的回答，如果文档内容与问题无关，请回答不知道。

            文档内容：{{#context#}}

            问题：{{#1744598867498.text#}}

            回答：'
        selected: false
        title: LLM 子问题回答
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744593775987'
      parentId: '1744593592934'
      position:
        x: 747.7142857142865
        y: 69.28571428571433
      positionAbsolute:
        x: 1079.7142857142865
        y: 425.28571428571433
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        context:
          enabled: true
          variable_selector:
          - conversation
          - finalResult
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-72B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 292f5968-ff62-4e66-915b-85b9fb8ecfb1
          role: system
          text: '请根据以下检索到的文档内容和问题生成一个详细且准确的回答，如果文档内容与问题无关，请回答不知道。

            文档内容：{{#context#}}

            问题：{{#sys.query#}}

            回答：'
        selected: false
        title: LLM 3
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744595369084'
      position:
        x: 1740
        y: 356
      positionAbsolute:
        x: 1740
        y: 356
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: clear
          value: ''
          variable_selector:
          - conversation
          - finalResult
          write_mode: over-write
        - input_type: variable
          operation: clear
          value: ''
          variable_selector:
          - conversation
          - preQueryAnswer
          write_mode: over-write
        - input_type: variable
          operation: clear
          value: ''
          variable_selector:
          - conversation
          - preQuery
          write_mode: over-write
        selected: false
        title: 变量赋值 4
        type: assigner
        version: '2'
      height: 142
      id: '1744597368675'
      position:
        x: 2042
        y: 356
      positionAbsolute:
        x: 2042
        y: 356
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744593592934'
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-7B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: be8f039e-171d-4b3a-844c-f3dae2b1c648
          role: system
          text: '您是一个问题生成器，根据提供的上下文和问题生成一个新的问题。用中文回答。会提供与该问题相关的推理步骤（包含子问题与对应的答案），根据上下文和之前的推理，返回一个可以从上下文中回答的问题:

            1.这个新问题可以帮助回答原始问题，与原始问题密切相关。

            2.可以是原问题的子问题，或者是解答原问题需要的一个步骤中需要的问题。

            如果无法从上下文中提取更多信息，则提供“无”作为答案。下面给出了一个示例:

            ----示例----

            问题:2020年澳大利亚网球公开赛冠军获得了多少个大满贯冠军？

            之前的推理问题:谁是2020年澳大利亚网球公开赛的冠军？

            推理问题的答案:提供了2020年澳大利亚网球公开赛冠军的名字（假设为李四）

            新问题:李四获得了多少个网球大满贯冠军？

            -----------

            问题:{{#sys.query#}}

            之前的推理问题:{{#conversation.preQuery#}}

            推理问题的答案:{{#conversation.preQueryAnswer#}}

            新问题:'
        selected: false
        title: LLM 子问题生成器
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744598867498'
      parentId: '1744593592934'
      position:
        x: 128
        y: 66.57142857142856
      positionAbsolute:
        x: 460
        y: 422.57142857142856
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1744598867498'
          - text
          variable_selector:
          - conversation
          - preQuery
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1744593775987'
          - text
          variable_selector:
          - conversation
          - preQueryAnswer
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1744593758714'
          - result
          variable_selector:
          - conversation
          - finalResult
          write_mode: over-write
        loop_id: '1744593592934'
        selected: false
        title: 变量赋值 3
        type: assigner
        version: '2'
      height: 142
      id: '1744599637553'
      parentId: '1744593592934'
      position:
        x: 1042.7753657437788
        y: 67.85714285714278
      positionAbsolute:
        x: 1374.7753657437788
        y: 423.8571428571428
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        author: tanchg
        desc: ''
        height: 88
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"每次循环涉及
          LLM调用 + 向量检索，计算成本较高","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"因此此处循环次数固定为3次，如遇复杂问题，可调整为5次，或者新增变量控制循环次数","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: blue
        title: ''
        type: ''
        width: 369
      height: 88
      id: '1744600859466'
      position:
        x: 768.6458068146221
        y: 237.143561555805
      positionAbsolute:
        x: 768.6458068146221
        y: 237.143561555805
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 369
    - data:
        author: tanchg
        desc: ''
        height: 136
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"多步查询转换：通过循环生成单个子问题并回答，将用户问题、上一个子问题以及对应的答案作为下一个子问题的上下文，循环结束时回答原始问题。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":"font-size:
          16px;"}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: blue
        title: ''
        type: ''
        width: 385
      height: 136
      id: '1744698451881'
      position:
        x: 41.90410320082185
        y: 694.383273440675
      positionAbsolute:
        x: 41.90410320082185
        y: 694.383273440675
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 385
    viewport:
      x: 106.7129077852453
      y: -31.62204338889353
      zoom: 0.9525530078957976
