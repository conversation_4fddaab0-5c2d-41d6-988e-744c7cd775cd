app:
  description: 新版写作助手，集成模板提取和填充功能，支持多文件上传
  icon: ✍️
  icon_background: '#E6F7FF'
  mode: advanced-chat
  name: 新版写作助手
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/moonshot:0.0.4@684e36cde2a78f7edd6faa0d07e530d8042115677b0d42acfc07fce9407d52e9
- current_identifier: null
  type: package
  value:
    plugin_unique_identifier: fetus/template-tools:0.0.6@f16d6eed6349b0cb82f6e179578b6b529a3338c6b7f9be626fb50a6a01b5d894
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions: []
      allowed_file_types:
      - image
      - document
      allowed_file_upload_methods:
      - remote_url
      - local_file
      enabled: true
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: 欢迎使用写作助手！请上传您的模板文件，并提供相关信息，我将帮您生成内容。
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
    - 如何使用这个写作助手？
    - 支持哪些类型的模板？
    - 如何获得最佳效果？
    suggested_questions_after_answer:
      enabled: true
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: document-extractor
      id: start-to-document-extractor
      source: start-node
      sourceHandle: source
      target: document-extractor-node
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: document-extractor
        targetType: code
      id: document-extractor-to-extract-placeholders
      source: document-extractor-node
      sourceHandle: source
      target: extract-placeholders-node
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: llm
      id: extract-placeholders-to-llm
      source: extract-placeholders-node
      sourceHandle: source
      target: combined-llm-node
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: code
      id: llm-to-code
      source: combined-llm-node
      sourceHandle: source
      target: extract-json-node
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: code
      id: code-to-format-node
      source: extract-json-node
      sourceHandle: source
      target: format-data-node
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: answer
      id: format-to-answer
      source: format-data-node
      sourceHandle: source
      target: answer-node
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: answer
        targetType: end
      id: answer-to-end
      source: answer-node
      sourceHandle: source
      target: end-node
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: 开始节点，收集用户输入和模板文件
        selected: false
        title: 开始
        type: start
        variables:
        - label: 语气
          max_length: 256
          options:
          - 正式
          - 友好
          - 专业
          - 简洁
          - 详细
          required: true
          type: select
          variable: tone
        - label: 语言
          max_length: 256
          options:
          - 中文
          - 英文
          - 中英双语
          required: true
          type: select
          variable: lang
        - label: 长度要求
          max_length: 256
          options:
          - 简短
          - 中等
          - 详细
          required: true
          type: select
          variable: length
        - label: 输出格式
          max_length: 256
          options:
          - 段落
          - 列表
          - 表格
          required: false
          type: text-input
          variable: format
        - label: 知识库ID（逗号分隔）
          max_length: 256
          options: []
          required: false
          type: text-input
          variable: personalLibs
        - label: 用户Token
          max_length: 100
          options: []
          required: false
          type: text-input
          variable: token
        - label: 租户ID
          max_length: 256
          options: []
          required: false
          type: text-input
          variable: tenantid
        - allowed_file_extensions: []
          allowed_file_types:
          - image
          - document
          allowed_file_upload_methods:
          - local_file
          - remote_url
          label: 参考文件（最多5个）
          max_length: 5
          options: []
          required: false
          type: file-list
          variable: files
        - allowed_file_extensions:
          - docx
          - doc
          - xlsx
          - xls
          allowed_file_types:
          - document
          allowed_file_upload_methods:
          - local_file
          - remote_url
          label: 输出模板
          max_length: 1
          options: []
          required: true
          type: file
          variable: outputTemplate
      height: 326
      id: start-node
      position:
        x: -49
        y: 149
      positionAbsolute:
        x: -49
        y: 149
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '## 写作助手结果

          ### 模板解析
          ```
          {{#document-extractor-node.text#}}
          ```

          ### 提取的占位符 (共{{#extract-placeholders-node.placeholders_count#}}个)
          {{#extract-placeholders-node.placeholders_list#}}

          ### 填充内容 (JSON格式)
          ```json
          {{#format-data-node.replacements#}}
          ```

          ### 处理状态
          - 模板提取：{{#extract-placeholders-node.result#}}
          - 内容生成：{{#extract-json-node.result#}}
          - 数据格式化：{{#format-data-node.result#}}'
        desc: 显示处理结果
        selected: false
        title: 结果展示
        type: answer
        variables:
        - value_selector:
          - document-extractor-node
          - text
          variable: document_extractor_text
        - value_selector:
          - extract-placeholders-node
          - placeholders_count
          variable: placeholders_count
        - value_selector:
          - extract-placeholders-node
          - placeholders_list
          variable: placeholders_list
        - value_selector:
          - extract-placeholders-node
          - result
          variable: extract_placeholders_result
        - value_selector:
          - combined-llm-node
          - text
          variable: llm_text
        - value_selector:
          - extract-json-node
          - result
          variable: extract_json_result
        - value_selector:
          - format-data-node
          - replacements
          variable: replacements
        - value_selector:
          - format-data-node
          - result
          variable: format_data_result
      height: 218
      id: answer-node
      position:
        x: 1519
        y: 149
      positionAbsolute:
        x: 1519
        y: 149
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: 解析模板文件
        is_array_file: false
        selected: false
        title: 模板解析
        type: document-extractor
        variable_selector:
        - start-node
        - outputTemplate
      height: 120
      id: document-extractor-node
      position:
        x: 263
        y: 149
      positionAbsolute:
        x: 263
        y: 149
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\nimport re\n\ndef main(template_text):\n    # 确保template_text是字符串\n    if not isinstance(template_text, str):\n        if template_text is None:\n            template_text = \"\"\n        else:\n            try:\n                # 尝试将非字符串类型转换为字符串\n                template_text = str(template_text)\n            except:\n                template_text = \"\"\n    \n    # 提取所有的占位符，格式为[xxx]\n    placeholder_pattern = r'\\[(.*?)\\]'\n    placeholders = re.findall(placeholder_pattern, template_text)\n    \n    # 去重，因为同一个占位符可能在文档中出现多次\n    unique_placeholders = list(set(placeholders))\n    \n    # 构建templateJson结构\n    template_json = {}\n    for placeholder in unique_placeholders:\n        placeholder_with_brackets = f\"[{placeholder}]\"\n        template_json[placeholder_with_brackets] = {\n            \"type\": 1,\n            \"description\": f\"填写{placeholder}\"\n        }\n    \n    # 构建占位符列表，用于LLM提示词\n    placeholders_formatted = []\n    for placeholder in unique_placeholders:\n        placeholders_formatted.append(f\"- [{placeholder}]：填写{placeholder}\")\n    \n    return {\n        \"result\": \"成功提取模板占位符\",\n        \"template_structure\": json.dumps(template_json, ensure_ascii=False),\n        \"placeholders_count\": len(unique_placeholders),\n        \"placeholders_list\": \"\\n\".join(placeholders_formatted)\n    }"
        code_language: python3
        desc: 提取模板中的占位符
        outputs:
          placeholders_count:
            type: number
          placeholders_list:
            type: string
          result:
            type: string
          template_structure:
            type: string
        selected: false
        title: 提取占位符
        type: code
        variables:
        - value_selector:
          - document-extractor-node
          - text
          variable: template_text
      height: 82
      id: extract-placeholders-node
      position:
        x: 400
        y: 149
      positionAbsolute:
        x: 400
        y: 149
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - start-node
          - document-extractor-node
          - extract-placeholders-node
        desc: 同时提取模板和填充内容
        model:
          completion_params: {}
          mode: chat
          name: moonshot-v1-128k
          provider: langgenius/moonshot/moonshot
        prompt_template:
        - id: system-prompt
          role: system
          text: "# 任务描述\n你是一位专业的文档模板处理专家，需要为文档模板中的占位符生成合适的内容。\n\n# 用户输入信息\n- 语气：{{#start-node.tone#}}\n- 语言：{{#start-node.lang#}}\n- 长度要求：{{#start-node.length#}}\n- 输出格式：{{#start-node.format#}}\n- 参考文件：\n  - 用户输出模板：{{#document-extractor-node.text#}}\n  - 用户输入：{{#sys.query#}}\n\n# 模板占位符\n模板中包含以下占位符，你需要为它们生成内容：\n{{#extract-placeholders-node.placeholders_list#}}\n\n# 处理步骤\n为占位符生成填充内容：\n- 根据用户提供的语气、语言、长度要求和格式偏好\n- 结合参考文件中的信息（如果有）\n- 为每个占位符生成高质量、符合要求的内容\n- 确保生成内容与占位符的预期用途匹配\n\n# 输出要求\n请直接输出一个标准JSON对象，其中键名为占位符（包含方括号），值为对应的填充内容。\n\n输出格式示例：\n{\n  \"[姓名]\": \"张三\",\n  \"[部门]\": \"技术部\",\n  \"[职位]\": \"工程师\"\n}\n\n# 注意事项\n1. 只输出JSON对象，不要有其他任何文字说明\n2. 保持JSON格式有效且完整\n3. 所有键名和字符串值必须使用双引号\n4. 键名必须与提供的占位符完全相同，包括方括号\n5. 确保生成的内容符合用户指定的语气、语言和长度要求\n6. 确保生成的内容简洁明了，避免过长的文本"
        selected: false
        title: 模板提取与填充
        type: llm
        variables: []
        vision:
          enabled: false
      height: 118
      id: combined-llm-node
      position:
        x: 559
        y: 149
      positionAbsolute:
        x: 559
        y: 149
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\nimport re\n\ndef main(text, template_structure):\n    # 确保text是字符串\n    if not isinstance(text, str):\n        if text is None:\n            text = \"\"\n        else:\n            try:\n                text = str(text)\n            except:\n                text = \"\"\n    \n    # 确保template_structure是字符串\n    if not isinstance(template_structure, str):\n        if template_structure is None:\n            template_structure = \"{}\"\n        elif isinstance(template_structure, dict):\n            try:\n                template_structure = json.dumps(template_structure, ensure_ascii=False)\n            except:\n                template_structure = \"{}\"\n        else:\n            try:\n                template_structure = str(template_structure)\n            except:\n                template_structure = \"{}\"\n    \n    try:\n        # 解析templateJson\n        template_json = json.loads(template_structure)\n        \n        # 尝试直接解析LLM输出的JSON\n        try:\n            # 清理可能的前后缀文本，只保留JSON部分\n            json_text = text.strip()\n            # 查找第一个{和最后一个}之间的内容\n            start = json_text.find('{')\n            end = json_text.rfind('}')\n            if start >= 0 and end > start:\n                json_text = json_text[start:end+1]\n            \n            # 解析JSON\n            replace_json = json.loads(json_text)\n        except Exception as json_error:\n            # JSON解析失败，尝试使用正则表达式提取键值对\n            replace_json = {}\n            # 查找常见的字段模式，例如 \"[姓名]\": \"张三\"\n            field_pattern = r'\\\"\\[([^\\]]+)\\]\\\"\\s*:\\s*\\\"([^\\\"]*)\\\"'\n            matches = re.findall(field_pattern, text)\n            for field, value in matches:\n                replace_json[f\"[{field}]\"] = value\n            \n            if not replace_json:\n                raise Exception(f\"无法解析JSON: {str(json_error)}\")\n        \n        # 确保所有模板字段在replace_json中都有对应的值\n        for key in template_json.keys():\n            if key not in replace_json or not replace_json[key]:\n                replace_json[key] = f\"请填写{template_json[key]['description']}\"  # 默认值\n        \n        return {\n            \"result\": \"成功提取模板和填充内容\",\n            \"template_structure\": template_structure,\n            \"replacements\": json.dumps(replace_json, ensure_ascii=False),\n            \"error\": \"\",\n            \"original_text\": text\n        }\n    except Exception as e:\n        # 如果解析template_structure失败，尝试使用空对象\n        try:\n            template_json = json.loads(template_structure)\n        except:\n            template_json = {}\n            \n        return {\n            \"result\": \"处理失败\",\n            \"error\": str(e),\n            \"template_structure\": template_structure,\n            \"replacements\": json.dumps({key: f\"请填写{value['description']}\" for key, value in template_json.items()}, ensure_ascii=False),\n            \"original_text\": text\n        }"
        code_language: python3
        desc: 从LLM输出中提取JSON数据
        outputs:
          error:
            type: string
          original_text:
            type: string
          replacements:
            type: string
          result:
            type: string
          template_structure:
            type: string
        selected: false
        title: 提取JSON数据
        type: code
        variables:
        - value_selector:
          - combined-llm-node
          - text
          variable: text
        - value_selector:
          - extract-placeholders-node
          - template_structure
          variable: template_structure
      height: 82
      id: extract-json-node
      position:
        x: 867
        y: 149
      positionAbsolute:
        x: 867
        y: 149
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    - data:
        code: "import json\n\ndef main(replacements, template_structure) -> dict:\n    \"\"\"确保传递给模板填充工具的数据是正确的字符串格式\"\"\"\n    # 处理replacements\n    if not isinstance(replacements, str):\n        if isinstance(replacements, dict):\n            replacements = json.dumps(replacements, ensure_ascii=False)\n        elif replacements is None:\n            replacements = \"{}\"\n        else:\n            try:\n                replacements = str(replacements)\n            except:\n                replacements = \"{}\"\n    \n    # 处理template_structure\n    if not isinstance(template_structure, str):\n        if isinstance(template_structure, dict):\n            template_structure = json.dumps(template_structure, ensure_ascii=False)\n        elif template_structure is None:\n            template_structure = \"{}\"\n        else:\n            try:\n                template_structure = str(template_structure)\n            except:\n                template_structure = \"{}\"\n    \n    return {\n        \"replacements\": replacements,\n        \"template_structure\": template_structure,\n        \"result\": \"数据格式化完成\"\n    }"
        code_language: python3
        desc: 确保数据格式正确
        outputs:
          replacements:
            type: string
          result:
            type: string
          template_structure:
            type: string
        selected: false
        title: 格式化数据
        type: code
        variables:
        - value_selector:
          - extract-json-node
          - replacements
          variable: replacements
        - value_selector:
          - extract-json-node
          - template_structure
          variable: template_structure
      height: 82
      id: format-data-node
      position:
        x: 1012
        y: 149
      positionAbsolute:
        x: 1012
        y: 149
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: 结束节点
        selected: false
        title: 结束
        type: end
      height: 82
      id: end-node
      position:
        x: 1800
        y: 149
      positionAbsolute:
        x: 1800
        y: 149
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 52.5
      y: 48.5
      zoom: 1
