app:
  description: 0421版本，加了输入增强，输出增强，添加多文件上传
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: 【生产2】- 回复助手 shangzy
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: package
  value:
    plugin_unique_identifier: fetus/template-tools:0.0.7@8d493cb6efe3134993dd68a6fd7118894226df7d0654566144a87fa373d45a76
- current_identifier: null
  type: package
  value:
    plugin_unique_identifier: langgenius/openai_api_compatible:0.1.11@aeb88e81bb79db614de257ad66eddde90cede8a3917c986fbd2be46df84d2e23
- current_identifier: null
  type: package
  value:
    plugin_unique_identifier: fetus/ocr-tools:0.0.2@7491341f8a5414ed0d706b5d2a51d701e5de8eb2dee188cb4a0c0bf3d801dda5
kind: app
version: 0.2.0
workflow:
  conversation_variables:
  - description: 知识库检索信息
    id: 95ac6135-3c36-40d5-8aa8-56919d23cd4b
    name: KOWNLAGE_TEXT
    selector:
    - conversation
    - KOWNLAGE_TEXT
    value: ''
    value_type: string
  - description: ocr识别的文字
    id: 0a5af602-67b8-4242-a248-104beab9b599
    name: OCR_TEXT
    selector:
    - conversation
    - OCR_TEXT
    value: ''
    value_type: string
  - description: 模板解析
    id: d73c955f-6a84-4f11-9fe0-a97db1b5b071
    name: TEMPLATE_TEXT
    selector:
    - conversation
    - TEMPLATE_TEXT
    value: ''
    value_type: string
  - description: 用户上传文档类资料
    id: da68d90c-a255-4603-b853-13e52e4acc15
    name: DOCS_TEXT_ARRAY
    selector:
    - conversation
    - DOCS_TEXT_ARRAY
    value: []
    value_type: array[string]
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions: []
      allowed_file_types:
      - image
      - document
      allowed_file_upload_methods:
      - remote_url
      - local_file
      enabled: true
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 1
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1745834905987-source-1745830852081-target
      selected: false
      source: '1745834905987'
      sourceHandle: source
      target: '1745830852081'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: start
        targetType: if-else
      id: 1744883904148-source-1747359844690-target
      source: '1744883904148'
      sourceHandle: source
      target: '1747359844690'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: document-extractor
      id: 1747359844690-true-1747359886507-target
      source: '1747359844690'
      sourceHandle: 'true'
      target: '1747359886507'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: tool
      id: 1745830852081-source-1747379627271-target
      source: '1745830852081'
      sourceHandle: source
      target: '1747379627271'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: tool
        targetType: answer
      id: 1747379627271-source-1745830056980-target
      source: '1747379627271'
      sourceHandle: source
      target: '1745830056980'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: start
        targetType: if-else
      id: 1744883904148-source-1747637774201-target
      selected: false
      source: '1744883904148'
      sourceHandle: source
      target: '1747637774201'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: start
        targetType: if-else
      id: 1744883904148-source-1747637858141-target
      source: '1744883904148'
      sourceHandle: source
      target: '1747637858141'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: tool
      id: 1747637858141-true-1747379340928-target
      source: '1747637858141'
      sourceHandle: 'true'
      target: '1747379340928'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: tool
      id: 1747637774201-true-1747645125530-target
      source: '1747637774201'
      sourceHandle: 'true'
      target: '1747645125530'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: tool
        targetType: assigner
      id: 1747645125530--1747969627009-target
      source: '1747645125530'
      sourceHandle: source
      target: '1747969627009'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: tool
        targetType: assigner
      id: 1747379340928--1747969638279-target
      source: '1747379340928'
      sourceHandle: source
      target: '1747969638279'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: document-extractor
        targetType: assigner
      id: 1747359886507-source-1747969631607-target
      source: '1747359886507'
      sourceHandle: source
      target: '1747969631607'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: assigner
        targetType: variable-aggregator
      id: 1747969631607-source-1747638988670-target
      source: '1747969631607'
      sourceHandle: source
      target: '1747638988670'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: assigner
        targetType: variable-aggregator
      id: 1747969627009-source-1747638988670-target
      source: '1747969627009'
      sourceHandle: source
      target: '1747638988670'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: assigner
        targetType: variable-aggregator
      id: 1747969638279-source-1747638988670-target
      source: '1747969638279'
      sourceHandle: source
      target: '1747638988670'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: start
        targetType: if-else
      id: 1744883904148-source-1747971539764-target
      source: '1744883904148'
      sourceHandle: source
      target: '1747971539764'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: document-extractor
      id: 1747971539764-true-1745834730510-target
      source: '1747971539764'
      sourceHandle: 'true'
      target: '1745834730510'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: document-extractor
        targetType: assigner
      id: 1745834730510-source-1747971657314-target
      source: '1745834730510'
      sourceHandle: source
      target: '1747971657314'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: assigner
        targetType: variable-aggregator
      id: 1747971657314-source-1747638988670-target
      source: '1747971657314'
      sourceHandle: source
      target: '1747638988670'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: variable-aggregator
        targetType: if-else
      id: 1747638988670-source-1747971784794-target
      source: '1747638988670'
      sourceHandle: source
      target: '1747971784794'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 1747971784794-true-1745834905987-target
      source: '1747971784794'
      sourceHandle: 'true'
      target: '1745834905987'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 1747971784794-false-1747971989497-target
      source: '1747971784794'
      sourceHandle: 'false'
      target: '1747971989497'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 1747971989497-source-1747972085625-target
      source: '1747971989497'
      sourceHandle: source
      target: '1747972085625'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 语气
          max_length: 256
          options: []
          required: false
          type: text-input
          variable: tone
        - label: 语言
          max_length: 256
          options: []
          required: false
          type: text-input
          variable: lang
        - label: 长度
          max_length: 256
          options: []
          required: false
          type: text-input
          variable: length
        - label: 知识库id，逗号间隔字符串
          max_length: 256
          options: []
          required: false
          type: text-input
          variable: personalLibs
        - label: token
          max_length: 100
          options: []
          required: false
          type: text-input
          variable: Token
        - label: tenantid
          max_length: 256
          options: []
          required: false
          type: text-input
          variable: tenantid
        - allowed_file_extensions: []
          allowed_file_types:
          - document
          allowed_file_upload_methods:
          - local_file
          label: 文档文件（最多5个）
          max_length: 5
          options: []
          required: false
          type: file-list
          variable: docFiles
        - allowed_file_extensions: []
          allowed_file_types:
          - document
          allowed_file_upload_methods:
          - local_file
          - remote_url
          label: 输出模板
          max_length: 48
          options: []
          required: false
          type: file
          variable: outputTemplate
        - allowed_file_extensions: []
          allowed_file_types:
          - image
          allowed_file_upload_methods:
          - local_file
          label: 图片文件（最大5）
          max_length: 5
          options: []
          required: false
          type: file-list
          variable: imageFiles
        - label: 格式
          max_length: 256
          options: []
          required: false
          type: text-input
          variable: format
      height: 324
      id: '1744883904148'
      position:
        x: 30
        y: 362
      positionAbsolute:
        x: 30
        y: 362
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '文件内容：

          {{#1747379627271.text#}}

          文件下载地址：

          {{#1747379627271.files#}}'
        desc: ''
        selected: false
        title: 文件地址输出
        type: answer
        variables: []
      height: 138
      id: '1745830056980'
      position:
        x: 2766
        y: 528
      positionAbsolute:
        x: 2766
        y: 528
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: deepseek-chat
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: 5b2238f8-bfee-44c7-9d44-ca4d64f3492d
          role: system
          text: "{{#context#}}\n- Role: JSON模板填充专家\n- Constrains: 请确保填充信息都来源于用户提供的上下文。避免主观臆断和不实信息。在填充过程中，保持专业性和逻辑性，确保JSON格式的完整性和有效性。\n\
            - Skills: 你具备强大的JSON处理能力、文本分析能力、逻辑判断能力以及对数据结构的专业知识，能够准确地识别JSON模板中需要填充的字段，并根据上下文进行高质量的填充。\n\
            - OutputFormat: 仅输出填充内容后的完整JSON，保证JSON格式的完整性和有效性。输出的JSON必须保持原始键名完全不变，只替换值部分。\n\
            - Workflow:\n  1. 接收两个输入：JSON模板结构和用户提供的上下文内容。\n  2. 分析JSON模板中的每个键（通常是文档中的占位符，如\"\
            [员工姓名]\"）及其对应的type和description。\n  3. 从用户提供的上下文中提取总结与每个键相关的信息。\n     \
            \  上下文包含：\n     - 用户上传资料 ：{{#conversation.DOCS_TEXT_ARRAY#}}\n     - 图片识别信息：{{#conversation.OCR_TEXT#}}\n\
            \     - 知识库信息： {{#conversation.KOWNLAGE_TEXT#}}\n     - 用户输入信息：{{#sys.query#}}\n\
            注：如果信息有限，需要根据用户输入进行联想填充\n  4. 根据每个键的type值确定填充方式：\n     - type=1：直接替换占位符为上下文中的相关信息\n\
            \  5. 创建一个新的JSON结构，其中键名必须与{{#1745834905987.text#}}保持完全不变（保留原始的[...]格式），只将值部分替换为从上下文中提取的实际信息。\n\
            \  6. 确保填充的内容与JSON字段的数据类型匹配，并符合description中的要求。\n  7. 如遇到无法从上下文获取的信息，保留原键但值设为空字符串或适当的默认值。\n\
            - Examples:\n  - 例子1：\n    JSON模板结构：\n    ```json\n    {\n      \"[员工姓名]\"\
            : {\n        \"type\": 1,\n        \"description\": \"填写员工的全名\"\n    \
            \  },\n      \"[所属部门]\": {\n        \"type\": 1,\n        \"description\"\
            : \"填写员工所在的部门名称\"\n      }\n    }\n    ```\n    \n    上下文内容：\n    \"新入职员工张三，将在技术研发部担任高级开发工程师。\"\
            \n    \n    填充后的JSON：\n    ```json\n    {\n      \"[员工姓名]\": \"张三\",\n\
            \      \"[所属部门]\": \"技术研发部\"\n    }\n    ```\n请以压缩格式的JSON输出结果，确保：\n1.\
            \ 所有键名使用双引号\n2. 所有字符串值使用双引号\n3. 不要在JSON中使用注释\n4. 不要在最后一个元素后添加逗号\n5. 使用正确的转义字符\n\
            6. 不包含任何换行符和不必要的空格\n7. 整个JSON应该是一行连续文本，没有格式化缩进"
        selected: false
        title: 变量替换
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1745830852081'
      position:
        x: 2158
        y: 528
      positionAbsolute:
        x: 2158
        y: 528
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_array_file: false
        selected: false
        title: 模板解析
        type: document-extractor
        variable_selector:
        - '1744883904148'
        - outputTemplate
      height: 92
      id: '1745834730510'
      position:
        x: 638
        y: 912
      positionAbsolute:
        x: 638
        y: 912
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: deepseek-chat
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: c506a5c7-a224-4e6e-84fe-509ddc8a5900
          role: system
          text: "{{#context#}}\n请帮我分析一个Word文档模板 {{#1745834730510.text#}}，并生成一个JSON格式的mapping结构。要求如下：\
            \  \n\n1. 分析规则：  \n-  仅扫描文档正文内容中，[]括起来的段落为需要填充的关键位置。\n2. 严格按以下输出格式，其中原文关键词要与原文完全一致：\
            \  \n{  \n  \"原文关键词\": {  \n    \"type\": 数字类型(1),  \n    \"description\"\
            : \"该位置需要填充内容的说明\"  \n  }  \n}  \n3. 具体要求：  \n- 请严格保持原文关键词完全一致，包括标点符号。\
            \  \n- type值使用数字1表示不同类型：  \n  * 1 = 直接替换   \n- description要简明扼要地说明该位置需要填充的内容类型\
            \  \n- 按文档中出现的顺序整理  \n\n4. 特别注意：  \n- []括起的内容为type=1的直接替换类型  \n\n请以压缩格式的JSON输出结果，确保：\n\
            1. 所有键名使用双引号\n2. 所有字符串值使用双引号\n3. 不要在JSON中使用注释\n4. 不要在最后一个元素后添加逗号\n5. 使用正确的转义字符\n\
            6. 不包含任何换行符和不必要的空格\n7. 整个JSON应该是一行连续文本，没有格式化缩进"
        selected: false
        title: 参数提取器
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1745834905987'
      position:
        x: 1854
        y: 528
      positionAbsolute:
        x: 1854
        y: 528
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: not empty
            id: d58cc6af-2192-4a13-adf8-61a5729b4caf
            sub_variable_condition:
              case_id: 98ebeed8-5659-45fa-8af7-1e35f8a99587
              conditions: []
              logical_operator: and
            value: ''
            varType: array[file]
            variable_selector:
            - '1744883904148'
            - docFiles
          id: 'true'
          logical_operator: and
        desc: ''
        selected: false
        title: 判断附件类型
        type: if-else
      height: 126
      id: '1747359844690'
      position:
        x: 334
        y: 362
      positionAbsolute:
        x: 334
        y: 362
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_array_file: true
        selected: false
        title: 用户上传资料
        type: document-extractor
        variable_selector:
        - '1744883904148'
        - docFiles
      height: 92
      id: '1747359886507'
      position:
        x: 638
        y: 362
      positionAbsolute:
        x: 638
        y: 362
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: 知识库ID
            ja_JP: 知识库ID
            pt_BR: 知识库ID
            zh_Hans: 知识库ID
          label:
            en_US: libId
            ja_JP: libId
            pt_BR: libId
            zh_Hans: libId
          llm_description: 知识库ID
          max: null
          min: null
          name: libId
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: 提问语句
            ja_JP: 提问语句
            pt_BR: 提问语句
            zh_Hans: 提问语句
          label:
            en_US: query
            ja_JP: query
            pt_BR: query
            zh_Hans: query
          llm_description: 提问语句
          max: null
          min: null
          name: query
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: 调用凭据
            ja_JP: 调用凭据
            pt_BR: 调用凭据
            zh_Hans: 调用凭据
          label:
            en_US: token
            ja_JP: token
            pt_BR: token
            zh_Hans: token
          llm_description: 调用凭据
          max: null
          min: null
          name: token
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: 租户ID
            ja_JP: 租户ID
            pt_BR: 租户ID
            zh_Hans: 租户ID
          label:
            en_US: tenantid
            ja_JP: tenantid
            pt_BR: tenantid
            zh_Hans: tenantid
          llm_description: 租户ID
          max: null
          min: null
          name: tenantid
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: true
          scope: null
          template: null
          type: string
        params:
          libId: ''
          query: ''
          tenantid: ''
          token: ''
        provider_id: 0851e232-51ca-4f6d-a24e-36694e9ca9e7
        provider_name: 【工作流工具】自定义知识库检索-shangzy
        provider_type: workflow
        selected: false
        title: 知识库检索
        tool_configurations: {}
        tool_description: 自定义知识库检索
        tool_label: 【工作流工具】自定义知识库检索-shangzy
        tool_name: custom_knowledge_retriever
        tool_parameters:
          libId:
            type: mixed
            value: '{{#1744883904148.personalLibs#}}'
          query:
            type: mixed
            value: '{{#sys.query#}}'
          tenantid:
            type: mixed
            value: '{{#1744883904148.tenantid#}}'
          token:
            type: mixed
            value: '{{#1744883904148.Token#}}'
        type: tool
      height: 54
      id: '1747379340928'
      position:
        x: 638
        y: 739
      positionAbsolute:
        x: 638
        y: 739
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: original docx template file
            ja_JP: original docx template file
            pt_BR: original docx template file
            zh_Hans: 原始DOCX模板文件
          label:
            en_US: original docx template file
            ja_JP: original docx template file
            pt_BR: original docx template file
            zh_Hans: 原始DOCX模板文件
          llm_description: original docx template file
          max: null
          min: null
          name: file
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: file
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: variable json mapping extracted from the template file
            ja_JP: variable json mapping extracted from the template file
            pt_BR: variable json mapping extracted from the template file
            zh_Hans: 从文件中提取出的模板变量JSON数据结构
          label:
            en_US: variable json mapping extracted from the template file
            ja_JP: variable json mapping extracted from the template file
            pt_BR: variable json mapping extracted from the template file
            zh_Hans: 从文件中提取出的模板变量JSON数据结构
          llm_description: variable json mapping extracted from the template file
          max: null
          min: null
          name: template_structure
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: actual content json mapping analyzed by llm or human that needs
              to be padded into the template file
            ja_JP: actual content json mapping analyzed by llm or human that needs
              to be padded into the template file
            pt_BR: actual content json mapping analyzed by llm or human that needs
              to be padded into the template file
            zh_Hans: 实际需要填充到模板中的JSON数据结构
          label:
            en_US: actual content json mapping analyzed by llm or human that needs
              to be padded into the template file
            ja_JP: actual content json mapping analyzed by llm or human that needs
              to be padded into the template file
            pt_BR: actual content json mapping analyzed by llm or human that needs
              to be padded into the template file
            zh_Hans: 实际需要填充到模板中的JSON数据结构
          llm_description: actual content json mapping analyzed by llm or human that
            needs to be padded into the template file
          max: null
          min: null
          name: replacements
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        params:
          file: ''
          replacements: ''
          template_structure: ''
        provider_id: fetus/template-tools/template-tools
        provider_name: fetus/template-tools/template-tools
        provider_type: builtin
        selected: false
        title: DOCX模板生成
        tool_configurations: {}
        tool_description: 基于段落，从原始docx文件生成docx模板
        tool_label: DOCX模板生成
        tool_name: docx-paragraph-template
        tool_parameters:
          file:
            type: variable
            value:
            - '1744883904148'
            - outputTemplate
          replacements:
            type: mixed
            value: '{{#1745830852081.text#}}'
          template_structure:
            type: mixed
            value: '{{#1745834905987.text#}}'
        type: tool
      height: 54
      id: '1747379627271'
      position:
        x: 2462
        y: 528
      positionAbsolute:
        x: 2462
        y: 528
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: not empty
            id: 299e69ba-b8fc-4930-8c9f-d35b5e22d78f
            value: ''
            varType: array[file]
            variable_selector:
            - '1744883904148'
            - imageFiles
          id: 'true'
          logical_operator: and
        desc: ''
        selected: false
        title: 判断图片附件
        type: if-else
      height: 126
      id: '1747637774201'
      position:
        x: 334
        y: 528
      positionAbsolute:
        x: 334
        y: 528
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: not empty
            id: f3877cab-6b2f-40b1-b28c-82282729e983
            value: ''
            varType: string
            variable_selector:
            - '1744883904148'
            - Token
          - comparison_operator: not empty
            id: ebc2ded1-987e-4bae-bb4d-9a29e1fbdbab
            value: ''
            varType: string
            variable_selector:
            - '1744883904148'
            - tenantid
          - comparison_operator: not empty
            id: c75fafa2-32fb-4ad2-8b8f-6c41bdf5301c
            value: ''
            varType: string
            variable_selector:
            - '1744883904148'
            - personalLibs
          id: 'true'
          logical_operator: and
        desc: ''
        selected: false
        title: 知识库判断
        type: if-else
      height: 178
      id: '1747637858141'
      position:
        x: 334
        y: 694
      positionAbsolute:
        x: 334
        y: 694
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        advanced_settings:
          group_enabled: true
          groups:
          - groupId: f40d10ea-05e6-4525-a82d-722cb08efab0
            group_name: Group1
            output_type: array[string]
            variables:
            - - conversation
              - DOCS_TEXT_ARRAY
          - groupId: d7a45e12-985a-4c69-8ecf-47affe024f07
            group_name: Group2
            output_type: string
            variables:
            - - conversation
              - TEMPLATE_TEXT
            - - conversation
              - OCR_TEXT
            - - conversation
              - KOWNLAGE_TEXT
        desc: ''
        output_type: array[string]
        selected: false
        title: 变量聚合器
        type: variable-aggregator
        variables:
        - - conversation
          - DOCS_TEXT_ARRAY
      height: 206
      id: '1747638988670'
      position:
        x: 1246
        y: 528
      positionAbsolute:
        x: 1246
        y: 528
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        default_value:
        - key: text
          type: string
          value: ''
        - key: json
          type: array[object]
          value: '[]'
        desc: ''
        error_strategy: default-value
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: OCR识别工具
            ja_JP: OCR识别工具
            pt_BR: OCR识别工具
            zh_Hans: OCR识别工具
          label:
            en_US: image
            ja_JP: image
            pt_BR: image
            zh_Hans: 图片
          llm_description: OCR识别工具
          max: null
          min: null
          name: image
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: file
        params:
          image: ''
        provider_id: fetus/ocr-tools/ocr-tools
        provider_name: fetus/ocr-tools/ocr-tools
        provider_type: builtin
        selected: false
        title: OCR识别工具
        tool_configurations: {}
        tool_description: OCR识别工具
        tool_label: OCR识别工具
        tool_name: ocr-tool
        tool_parameters:
          image:
            type: variable
            value:
            - '1744883904148'
            - imageFiles
        type: tool
      height: 90
      id: '1747645125530'
      position:
        x: 638
        y: 529
      positionAbsolute:
        x: 638
        y: 529
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1747645125530'
          - text
          variable_selector:
          - conversation
          - OCR_TEXT
          write_mode: over-write
        selected: false
        title: 变量赋值
        type: assigner
        version: '2'
      height: 88
      id: '1747969627009'
      position:
        x: 942
        y: 528
      positionAbsolute:
        x: 942
        y: 528
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1747359886507'
          - text
          variable_selector:
          - conversation
          - DOCS_TEXT_ARRAY
          write_mode: over-write
        selected: false
        title: 变量赋值 2
        type: assigner
        version: '2'
      height: 88
      id: '1747969631607'
      position:
        x: 942
        y: 362
      positionAbsolute:
        x: 942
        y: 362
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1747379340928'
          - text
          variable_selector:
          - conversation
          - KOWNLAGE_TEXT
          write_mode: over-write
        selected: false
        title: 变量赋值 3
        type: assigner
        version: '2'
      height: 88
      id: '1747969638279'
      position:
        x: 942
        y: 720
      positionAbsolute:
        x: 942
        y: 720
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: exists
            id: 8c3533b1-04df-4f49-bcc2-56f46dbf2af9
            value: ''
            varType: file
            variable_selector:
            - '1744883904148'
            - outputTemplate
          id: 'true'
          logical_operator: and
        desc: ''
        selected: false
        title: 判断模板
        type: if-else
      height: 126
      id: '1747971539764'
      position:
        x: 334
        y: 912
      positionAbsolute:
        x: 334
        y: 912
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1745834730510'
          - text
          variable_selector:
          - conversation
          - TEMPLATE_TEXT
          write_mode: over-write
        selected: false
        title: 变量赋值 4
        type: assigner
        version: '2'
      height: 88
      id: '1747971657314'
      position:
        x: 942
        y: 912
      positionAbsolute:
        x: 942
        y: 912
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: exists
            id: a7dbc15c-d1e9-409b-8889-33fed5bcce40
            value: ''
            varType: file
            variable_selector:
            - '1744883904148'
            - outputTemplate
          id: 'true'
          logical_operator: and
        desc: ''
        selected: false
        title: 判断文字、模板输出
        type: if-else
      height: 126
      id: '1747971784794'
      position:
        x: 1550
        y: 528
      positionAbsolute:
        x: 1550
        y: 528
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: deepseek-chat
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: dfbbb6c1-800f-4d78-af45-5b51f9f7d456
          role: system
          text: '根据下面的内容，以{{#1744883904148.tone#}}语气，用{{#1744883904148.lang#}}生成篇幅{{#1744883904148.length#}}的文字，文字风格为{{#1744883904148.format#}}

            以下是文字内容：

            {{#sys.query#}}'
        selected: false
        structured_output_enabled: false
        title: 文字输出
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1747971989497'
      position:
        x: 1862.5714285714284
        y: 694
      positionAbsolute:
        x: 1862.5714285714284
        y: 694
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1747971989497.text#}}'
        desc: ''
        selected: false
        title: 文字输出
        type: answer
        variables: []
      height: 104
      id: '1747972085625'
      position:
        x: 2178.5714285714294
        y: 694
      positionAbsolute:
        x: 2178.5714285714294
        y: 694
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 167.10118717937098
      y: -150.14220953300025
      zoom: 0.5305007982786393
