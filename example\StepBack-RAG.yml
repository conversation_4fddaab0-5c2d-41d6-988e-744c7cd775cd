app:
  description: 步退查询转换：基于原始问题生成一个更抽象的高层问题，进行召回并作为上下文回答原始问题。
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: 【测试】StepBack-RAG
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/siliconflow:0.0.8@217f973bd7ced1b099c2f0c669f1356bdf4cc38b8372fd58d7874f9940b95de3
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1744601786513-source-1744603227155-target
      source: '1744601786513'
      sourceHandle: source
      target: '1744603227155'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: knowledge-retrieval
      id: 1744603227155-source-1744603292100-target
      source: '1744603227155'
      sourceHandle: source
      target: '1744603292100'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: knowledge-retrieval
        targetType: llm
      id: 1744603292100-source-1744603315589-target
      source: '1744603292100'
      sourceHandle: source
      target: '1744603315589'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: end
      id: 1744603315589-source-1744603407057-target
      source: '1744603315589'
      sourceHandle: source
      target: '1744603407057'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: query
          max_length: 48
          options: []
          required: true
          type: text-input
          variable: query
      height: 88
      id: '1744601786513'
      position:
        x: 80
        y: 282
      positionAbsolute:
        x: 80
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-7B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: b5aa2193-4b07-4d89-abe5-999c4b335261
          role: system
          text: '您是一个问题生成器，给定一个具体问题，生成一个更抽象的高层问题，用于检索背景知识。用中文回答。

            具体问题：{{#1744601786513.query#}}

            抽象问题：'
        selected: false
        title: LLM 生成问题
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744603227155'
      position:
        x: 382
        y: 282
      positionAbsolute:
        x: 382
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        dataset_ids:
        - 2250ed95-a817-436a-b1d0-5c04baea5cc3
        desc: ''
        multiple_retrieval_config:
          reranking_enable: false
          reranking_mode: reranking_model
          reranking_model:
            model: netease-youdao/bce-reranker-base_v1
            provider: langgenius/siliconflow/siliconflow
          top_k: 4
        query_variable_selector:
        - '1744603227155'
        - text
        retrieval_mode: multiple
        selected: false
        title: 知识检索
        type: knowledge-retrieval
      height: 90
      id: '1744603292100'
      position:
        x: 684
        y: 282
      positionAbsolute:
        x: 684
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        context:
          enabled: true
          variable_selector:
          - '1744603292100'
          - result
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-72B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: df02e28a-2569-44c8-9e4e-23c4fe67b5d4
          role: system
          text: '请根据以下检索到的文档内容和问题生成一个详细且准确的回答，如果文档内容与问题无关，请回答不知道。

            文档内容：{{#context#}}

            问题：{{#1744601786513.query#}}

            回答：'
        selected: false
        title: LLM 回答
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744603315589'
      position:
        x: 986
        y: 282
      positionAbsolute:
        x: 986
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1744603315589'
          - text
          variable: answer
        selected: false
        title: 结束
        type: end
      height: 88
      id: '1744603407057'
      position:
        x: 1288
        y: 282
      positionAbsolute:
        x: 1288
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        author: tanchg
        desc: ''
        height: 113
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"步退查询转换：基于原始问题生成一个更抽象的高层问题，然后进行检索召回并作为上下文回答原始问题。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":"font-size:
          16px;"}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: blue
        title: ''
        type: ''
        width: 455
      height: 113
      id: '1744698518401'
      position:
        x: 143.37052645513563
        y: 447.9107071828545
      positionAbsolute:
        x: 143.37052645513563
        y: 447.9107071828545
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 455
    viewport:
      x: 426.65209953134627
      y: 235.85549681868173
      zoom: 0.734867267128043
