# Dify 写作助手工作流分析

## 一、需求分析

### 1. 功能需求
- 多文件上传和处理
- OCR图片识别
- 知识库检索
- 模板化文档生成
- 智能文本生成

### 2. 技术需求
- 支持多种文件格式
- 支持并行处理
- 支持模板解析
- 支持变量管理

## 二、节点分析

### 1. 输入节点
- start：接收用户输入参数（文件、模板、配置等）

### 2. 控制节点
- 4个if-else节点：判断附件类型、图片附件、知识库、模板
- 1个variable-aggregator：聚合多个来源的数据

### 3. 处理节点
- 2个document-extractor：处理用户上传资料和模板
- 3个tool节点：OCR识别、知识库检索、文档生成
- 2个llm节点：变量替换和文字输出
- 4个assigner节点：变量赋值

### 4. 输出节点
- 2个answer节点：分别处理文件输出和文字输出

## 三、数据流转

### 1. 输入流
- 用户文档 -> document-extractor -> assigner
- 图片文件 -> OCR工具 -> assigner
- 知识库参数 -> 知识库检索 -> assigner

### 2. 处理流
- 模板文件 -> 模板解析 -> 变量替换
- 多源数据 -> 变量聚合器 -> 智能处理

### 3. 输出流
- 模板场景 -> 文档生成 -> 文件输出
- 纯文本场景 -> 文字生成 -> 文本输出

## 四、详细节点说明

### 1. 输入节点（开始节点）
开始节点包含以下用户输入参数：
- tone（语气）：控制输出的语气风格
- lang（语言）：指定输出语言
- length（长度）：控制输出文本长度
- personalLibs（知识库ID）：指定要检索的知识库，支持多个，逗号分隔
- Token（认证令牌）：API访问令牌
- tenantid（租户ID）：租户标识
- docFiles（文档文件）：支持上传最多5个文档文件
- outputTemplate（输出模板）：文档模板文件
- imageFiles（图片文件）：支持上传最多5个图片文件
- format（格式）：指定输出格式

### 2. 条件判断流程
工作流程中包含4个关键的条件判断节点：
- 附件类型判断：检查是否有文档类型的附件上传，如果有则进入文档处理流程
- 图片附件判断：检查是否有图片类型的附件上传，如果有则进入OCR处理流程
- 知识库判断：检查是否提供了知识库相关参数（Token、tenantid、personalLibs），如果都存在则进入知识库检索流程
- 模板判断：检查是否上传了输出模板，如果有则进入模板处理流程

### 3. 并行处理流程
系统支持四个并行的处理流程：

#### 文档处理流程
- document-extractor节点：提取上传文档的文本内容
- assigner节点：将提取的文本内容赋值给DOCS_TEXT_ARRAY变量

#### 图片处理流程
- OCR工具节点：识别图片中的文字内容
- assigner节点：将识别的文字赋值给OCR_TEXT变量

#### 知识库处理流程
- 知识库检索工具：根据用户输入在指定知识库中检索相关内容
- assigner节点：将检索结果赋值给KOWNLAGE_TEXT变量

#### 模板处理流程
- document-extractor节点：解析模板文件内容
- assigner节点：将模板内容赋值给TEMPLATE_TEXT变量

### 4. 数据聚合处理
variable-aggregator节点的处理流程：
- 收集所有来源的数据：DOCS_TEXT_ARRAY、OCR_TEXT、KOWNLAGE_TEXT、TEMPLATE_TEXT
- 将数据分组整理，便于后续处理
- 确保数据的完整性和一致性

### 5. 智能处理流程
根据是否存在模板，系统有两个处理分支：

#### 模板处理分支
- 参数提取器（LLM节点）：分析模板结构，提取需要填充的参数
- 变量替换（LLM节点）：根据上下文信息，智能填充模板参数
- 文档生成（Tool节点）：基于填充后的参数生成最终文档

#### 纯文本处理分支
- 文字输出（LLM节点）：根据用户需求和上下文信息生成文本内容
- 支持控制语气、语言、长度等属性

### 6. 输出处理
系统有两种输出形式：
- 文件输出：生成的文档文件及其下载地址
- 文本输出：生成的文本内容

## 五、变量使用详细说明

### 1. 变量定义和类型
系统中定义了四个核心变量，每个变量都有特定的用途和格式要求：

#### DOCS_TEXT_ARRAY
- 类型：array[string]
- 用途：存储文档解析结果
- 格式：["文档1内容", "文档2内容", ...]
- 默认值：空数组 []

#### OCR_TEXT
- 类型：string
- 用途：存储OCR识别结果
- 格式："识别出的文本内容"
- 默认值：空字符串 ""

#### KOWNLAGE_TEXT
- 类型：string
- 用途：存储知识库检索结果
- 格式："检索到的知识库内容"
- 默认值：空字符串 ""

#### TEMPLATE_TEXT
- 类型：string
- 用途：存储模板解析结果
- 格式："模板文件内容"
- 默认值：空字符串 ""

### 2. 变量生命周期
- 初始化：工作流启动时，所有变量被初始化为默认值
- 赋值：通过assigner节点进行赋值，每个变量只能被赋值一次
- 使用：在LLM节点和Tool节点中可以引用变量值
- 销毁：工作流结束时自动销毁

### 3. 变量依赖关系
变量之间存在以下依赖关系：
- DOCS_TEXT_ARRAY → variable-aggregator → LLM处理
- OCR_TEXT → variable-aggregator → LLM处理
- KOWNLAGE_TEXT → variable-aggregator → LLM处理
- TEMPLATE_TEXT → 模板参数提取 → 变量替换 → 文档生成

## 六、模板使用指南

### 1. 模板格式规范
模板文件支持以下格式和语法：
- 文件格式：支持.txt、.md、.docx格式
- 变量语法：使用 {{变量名}} 表示需要替换的变量
- 条件语法：支持 {{#if 条件}}...{{/if}} 条件块
- 循环语法：支持 {{#each 数组}}...{{/each}} 循环块

### 2. 模板变量定义
```markdown
# 示例模板
标题：{{title}}
作者：{{author}}

## 文档内容
{{#each DOCS_TEXT_ARRAY}}
- {{this}}
{{/each}}

## OCR识别结果
{{#if OCR_TEXT}}
{{OCR_TEXT}}
{{/if}}

## 知识库参考
{{KOWNLAGE_TEXT}}
```

### 3. 常见模板问题
- 变量未定义：确保模板中使用的变量名与系统变量名完全匹配
- 格式错误：检查变量语法是否正确，括号是否配对
- 编码问题：确保模板文件使用UTF-8编码

## 七、接口调用说明

### 1. 知识库API
知识库检索接口参数说明：
- Token：API访问令牌，必填
- tenantid：租户ID，必填
- personalLibs：知识库ID列表，多个ID用逗号分隔
- query：检索关键词，支持模糊匹配
- limit：返回结果数量限制，默认10条

### 2. OCR服务
OCR服务调用参数：
- 支持的图片格式：PNG、JPG、JPEG、BMP
- 图片大小限制：单个文件不超过10MB
- 图片质量要求：清晰度不低于300dpi
- 识别语言：支持中文、英文、数字

### 3. 文档处理服务
文档处理服务支持以下功能：
- 文档格式：支持DOC、DOCX、PDF、TXT等格式
- 文件大小：单个文件不超过15MB
- 批量处理：单次最多处理5个文件
- 文本提取：自动提取文档中的文本内容

### 4. 接口调用示例
```bash
# 知识库检索示例
POST /api/knowledge/search
Headers:
  Token: your_token
  Content-Type: application/json
Body:
{
  "tenantid": "tenant_123",
  "personalLibs": "lib1,lib2",
  "query": "搜索关键词",
  "limit": 10
}

# OCR识别示例
POST /api/ocr/recognize
Headers:
  Content-Type: multipart/form-data
Body:
  file: <图片文件>
  language: "zh_CN"
```

## 八、完整DSL代码

完整的DSL代码包含以下几个主要部分：

### 1. 应用基础配置
```yaml
app:
  description: 0421版本，加了输入增强，输出增强，添加多文件上传
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: 【生产2】- 写作助手 shangzy
  use_icon_as_answer_icon: false
```

### 2. 依赖配置
```yaml
dependencies:
- current_identifier: null
  type: package
  value:
    plugin_unique_identifier: fetus/template-tools:0.0.7@8d493cb6efe3134993dd68a6fd7118894226df7d0654566144a87fa373d45a76
- current_identifier: null
  type: package
  value:
    plugin_unique_identifier: langgenius/openai_api_compatible:0.1.11@aeb88e81bb79db614de257ad66eddde90cede8a3917c986fbd2be46df84d2e23
- current_identifier: null
  type: package
  value:
    plugin_unique_identifier: fetus/ocr-tools:0.0.2@7491341f8a5414ed0d706b5d2a51d701e5de8eb2dee188cb4a0c0bf3d801dda5
```

### 3. 工作流变量配置
```yaml
workflow:
  conversation_variables:
  - description: 知识库检索信息
    id: 95ac6135-3c36-40d5-8aa8-56919d23cd4b
    name: KOWNLAGE_TEXT
    selector:
    - conversation
    - KOWNLAGE_TEXT
    value: ''
    value_type: string
  - description: ocr识别的文字
    id: 0a5af602-67b8-4242-a248-104beab9b599
    name: OCR_TEXT
    selector:
    - conversation
    - OCR_TEXT
    value: ''
    value_type: string
  - description: 模板解析
    id: d73c955f-6a84-4f11-9fe0-a97db1b5b071
    name: TEMPLATE_TEXT
    selector:
    - conversation
    - TEMPLATE_TEXT
    value: ''
    value_type: string
  - description: 用户上传文档类资料
    id: da68d90c-a255-4603-b853-13e52e4acc15
    name: DOCS_TEXT_ARRAY
    selector:
    - conversation
    - DOCS_TEXT_ARRAY
    value: []
    value_type: array[string]
```

### 4. 功能特性配置
```yaml
  features:
    file_upload:
      allowed_file_extensions: []
      allowed_file_types:
      - image
      - document
      allowed_file_upload_methods:
      - remote_url
      - local_file
      enabled: true
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 1
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
```

### 5. 工作流图配置
工作流图配置包含两个主要部分：edges（边）和nodes（节点）。这是整个工作流的核心部分，定义了所有节点之间的连接关系和每个节点的具体配置。

#### 5.1 边配置（edges）
```yaml
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1745834905987-source-1745830852081-target
      selected: false
      source: '1745834905987'
      sourceHandle: source
      target: '1745830852081'
      targetHandle: target
      type: custom
      zIndex: 0
```

#### 5.2 节点配置（nodes）
```yaml
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 语气
          max_length: 256
          options: []
          required: false
          type: text-input
          variable: tone
        - label: 语言
          max_length: 256
          options: []
          required: false
          type: text-input
          variable: lang
```

完整的DSL文件包含987行代码，涵盖了：
- 23个节点的详细配置
- 22条边的连接关系
- 4个工作流变量
- 3个依赖包
- 完整的功能特性配置

## 九、完整可导入的DSL代码

**注意：完整的987行DSL代码请直接从源文件 `【生产2】- 写作助手 shangzy (8).yml` 复制使用，可以直接导入到Dify工作流中。**

该文件包含了完整的工作流配置，包括所有节点、边、变量和依赖关系的详细定义。

## 总结

这个写作助手工作流是一个功能完整、设计精良的智能文档处理系统，具有以下特点：

1. **多模态输入支持**：支持文档、图片、知识库等多种输入源
2. **并行处理能力**：多个处理流程可以并行执行，提高效率
3. **智能模板处理**：支持复杂的模板语法和变量替换
4. **灵活的输出方式**：根据不同场景提供文件或文本输出
5. **完善的错误处理**：通过条件判断确保流程的健壮性

该工作流可以广泛应用于文档生成、内容创作、知识管理等场景，是一个非常实用的AI工作流解决方案。 