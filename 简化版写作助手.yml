app:
  description: 简化版写作助手，直接使用LLM生成内容
  icon: ✍️
  icon_background: '#E6F7FF'
  mode: advanced-chat
  name: 简化版写作助手
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/moonshot:0.0.4@684e36cde2a78f7edd6faa0d07e530d8042115677b0d42acfc07fce9407d52e9
kind: app
version: "0.1.5"
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions: []
      allowed_file_types:
      - image
      - document
      allowed_file_upload_methods:
      - remote_url
      - local_file
      enabled: true
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: 欢迎使用简化版写作助手！请上传您的模板文件，并提供相关信息，我将帮您生成内容。
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
    - 如何使用这个写作助手？
    - 支持哪些类型的模板？
    - 如何获得最佳效果？
    suggested_questions_after_answer:
      enabled: true
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: llm
      id: start-to-llm
      source: start-node
      sourceHandle: source
      target: llm-node
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: llm-to-answer
      source: llm-node
      sourceHandle: source
      target: answer-node
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: answer
        targetType: end
      id: answer-to-end
      source: answer-node
      sourceHandle: source
      target: end-node
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: 开始节点，收集用户输入和模板文件
        selected: false
        title: 开始
        type: start
        variables:
        - label: 语气
          max_length: 256
          options:
          - 正式
          - 友好
          - 专业
          - 简洁
          - 详细
          required: true
          type: select
          variable: tone
        - label: 语言
          max_length: 256
          options:
          - 中文
          - 英文
          - 中英双语
          required: true
          type: select
          variable: lang
        - label: 长度要求
          max_length: 256
          options:
          - 简短
          - 中等
          - 详细
          required: true
          type: select
          variable: length
        - label: 输出格式
          max_length: 256
          options:
          - 段落
          - 列表
          - 表格
          required: false
          type: text-input
          variable: format
        - allowed_file_extensions:
          - docx
          - doc
          - xlsx
          - xls
          allowed_file_types:
          - document
          allowed_file_upload_methods:
          - local_file
          - remote_url
          label: 输出模板
          max_length: 1
          options: []
          required: true
          type: file
          variable: outputTemplate
      height: 326
      id: start-node
      position:
        x: -49
        y: 149
      positionAbsolute:
        x: -49
        y: 149
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - start-node
        desc: 处理模板和生成内容
        model:
          completion_params: {}
          mode: chat
          name: moonshot-v1-128k
          provider: langgenius/moonshot/moonshot
        prompt_template:
        - id: system-prompt
          role: system
          text: "# 任务描述\n你是一位专业的文档模板处理专家，需要为文档模板中的占位符生成合适的内容。\n\n# 用户输入信息\n- 语气：{{#start-node.tone#}}\n- 语言：{{#start-node.lang#}}\n- 长度要求：{{#start-node.length#}}\n- 输出格式：{{#start-node.format#}}\n- 用户输入：{{#sys.query#}}\n\n# 处理步骤\n1. 分析用户输入的内容\n2. 根据用户提供的语气、语言、长度要求和格式偏好生成内容\n3. 确保生成内容高质量、符合要求\n\n# 输出要求\n请生成符合用户需求的内容，格式清晰，内容丰富。"
        selected: false
        title: 内容生成
        type: llm
        variables: []
        vision:
          enabled: false
      height: 118
      id: llm-node
      position:
        x: 300
        y: 149
      positionAbsolute:
        x: 300
        y: 149
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '## 写作助手结果

          {{#llm-node.text#}}'
        desc: 显示处理结果
        selected: false
        title: 结果展示
        type: answer
        variables:
        - value_selector:
          - llm-node
          - text
          variable: llm_text
      height: 218
      id: answer-node
      position:
        x: 600
        y: 149
      positionAbsolute:
        x: 600
        y: 149
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: 结束节点
        selected: false
        title: 结束
        type: end
      height: 82
      id: end-node
      position:
        x: 900
        y: 149
      positionAbsolute:
        x: 900
        y: 149
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 52.5
      y: 48.5
      zoom: 1
