Markdown导出Word所需依赖安装与引入
1. 安装依赖
# 安装核心依赖
npm install html-docx-js file-saver marked
2. 在main.ts中引入
import { createApp } from 'vue'
import App from './App.vue'

// 如果需要在全局使用,可以这样引入
import htmlDocx from 'html-docx-js'
import { saveAs } from 'file-saver'
import { marked } from 'marked'

const app = createApp(App)
app.mount('#app')
3. 在组件中使用时引入
// 在需要使用的组件中按需引入
import htmlDocx from 'html-docx-js'
import { saveAs } from 'file-saver'
import { marked } from 'marked'
4. 依赖说明
html-docx-js: HTML转Word文档
file-saver: 文件保存/下载
marked: Markdown转HTML