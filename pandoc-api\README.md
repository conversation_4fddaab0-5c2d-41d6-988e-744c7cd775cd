# Pandoc API 服务

这是一个基于Docker的Pandoc API服务，支持通过API进行文档格式转换，具有以下特点：

- 支持字符串和文件两种输入方式
- 完整的中文支持，包括宋体字体
- RESTful API接口
- 支持多种文档格式转换

## 部署步骤

1. 构建Docker镜像：

```bash
docker build -t pandoc-api .
```

2. 运行Docker容器：

```bash
docker run -d -p 8000:8000 --name pandoc-service pandoc-api
```

## API使用示例

### 1. 健康检查

```bash
curl http://localhost:8000/health
```

### 2. 转换文本（Markdown到HTML）

```bash
curl -X POST "http://localhost:8000/convert/text" \
  -F "content=# 你好，世界！\n\n这是一个**中文**测试。" \
  -F "from_format=markdown" \
  -F "to_format=html"
```

### 3. 转换文件（如Markdown到PDF）

```bash
curl -X POST "http://localhost:8000/convert/file" \
  -F "file=@/path/to/document.md" \
  -F "to_format=pdf" \
  -F "options=--pdf-engine=xelatex --variable=mainfont:SimSun"
```

## 注意事项

1. 如果需要使用宋体，可以将宋体字体文件（如simsun.ttc）放在`fonts`目录中，构建时会自动复制到镜像中。

2. 默认情况下，服务使用开源的思源黑体/思源宋体作为替代宋体。

3. 如果需要生成PDF，建议使用`--pdf-engine=xelatex`选项以获得更好的中文支持。

4. API服务默认在8000端口运行，可以根据需要修改。

## API文档

启动服务后，可以通过访问 http://localhost:8000/docs 查看完整的API文档。