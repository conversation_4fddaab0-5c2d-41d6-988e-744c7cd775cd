# Dify工作流基础知识

## 1. 工作流基本概念

### 1.1 什么是Dify工作流
Dify工作流是Dify平台提供的一种低代码方式，用于构建复杂的AI应用流程。工作流允许用户通过可视化界面或DSL(领域特定语言)文件定义一系列节点和它们之间的连接，从而实现复杂的业务逻辑和数据处理流程。

### 1.2 工作流的核心组件
- **节点(Nodes)**: 工作流中的基本处理单元，每个节点执行特定的功能
- **边(Edges)**: 连接节点的线，定义数据流动的路径和方向
- **变量(Variables)**: 节点间传递数据的载体
- **上下文(Context)**: 允许节点访问其他节点的输出数据

### 1.3 工作流执行流程
1. 从**开始节点**接收初始输入
2. 按照定义的边的方向，数据流经各个节点进行处理
3. 每个节点处理完成后，将结果传递给下一个节点
4. 最终输出结果返回给用户

## 2. DSL文件结构

### 2.1 顶层结构
Dify工作流DSL文件是YAML格式，必须包含以下顶层结构：

```yaml
kind: app                # 固定值，表示这是一个应用定义
version: "0.1.5"         # 版本号，必须是字符串类型，用引号包裹
app:                     # 应用定义
  mode: workflow         # 应用模式，必须设置为workflow
  name: "应用名称"        # 应用名称
  description: "应用描述" # 应用描述
  icon: "🤖"             # 应用图标(emoji)
  icon_background: "#FFFFFF" # 图标背景色
  use_icon_as_answer_icon: false # 是否使用应用图标作为回答图标
  
  nodes:                 # 节点定义部分
    # 节点定义...
  
  edges:                 # 边定义部分
    # 边定义...
```

### 2.2 节点定义结构
节点定义位于`app.nodes`下，每个节点都有一个唯一的ID和一系列属性：

```yaml
nodes:
  start_node:            # 节点ID，在工作流中唯一
    type: start          # 节点类型
    title: "开始节点"     # 节点标题
    variables: []        # 节点变量定义，即使没有也要包含空数组
    outputs:             # 节点输出定义
      text:              # 输出变量名
        type: string     # 输出变量类型
    availablePrevNodes: [] # 可用的前置节点，即使没有也要包含空数组
    availableNextNodes: [] # 可用的后置节点，即使没有也要包含空数组
```

### 2.3 边定义结构
边定义位于`app.edges`下，定义节点之间的连接关系：

```yaml
edges:
  - source: start_node     # 源节点ID
    target: process_node   # 目标节点ID
    sourceHandle: ""       # 源节点连接点，通常为空字符串
    targetHandle: ""       # 目标节点连接点，通常为空字符串
    sourceType: start      # 源节点类型
    targetType: llm        # 目标节点类型
```

### 2.4 必要字段和可选字段

#### 必要字段
- `kind`: 必须为"app"
- `version`: 必须为字符串类型的版本号，如"0.1.5"
- `app.mode`: 必须为"workflow"
- `app.name`: 应用名称
- `app.nodes`: 节点定义
- `app.edges`: 边定义

#### 每个节点的必要字段
- `type`: 节点类型
- `title`: 节点标题
- `variables`: 变量定义数组(即使为空)
- `outputs`: 输出定义
- `availablePrevNodes`: 可用前置节点数组(即使为空)
- `availableNextNodes`: 可用后置节点数组(即使为空)

## 3. 变量和上下文

### 3.1 变量定义
变量是节点间传递数据的载体，在节点的`variables`属性中定义：

```yaml
variables:
  - name: user_input      # 变量名
    type: string          # 变量类型
    default: ""           # 默认值
```

### 3.2 上下文配置
上下文允许节点访问其他节点的输出数据，通过`context`属性配置：

```yaml
context:
  enabled: true           # 启用上下文
  variable_selector:      # 选择要访问的变量
    - start_node          # 节点名称
    - text                # 该节点的变量名
```

### 3.3 在提示词中使用上下文
在LLM节点的提示词中，使用`{{#context#}}`标记来引入上下文：

```yaml
prompt_template:
  - id: system-prompt
    role: system
    text: '{{#context#}}
      你是一个助手...'
```

## 4. 重要注意事项

### 4.1 版本字段必须是字符串
`version`字段必须使用引号包裹，确保它被解析为字符串而不是浮点数：
```yaml
version: "0.1.5"  # 正确
version: 0.1.5    # 错误，会被解析为浮点数
```

### 4.2 应用模式必须明确指定
必须将`app.mode`设置为"workflow"，否则会出现"Missing workflow data"错误：
```yaml
app:
  mode: workflow  # 必须明确指定为workflow
```

### 4.3 节点必须包含所有必要属性
即使某些属性为空，也必须包含在定义中，例如：
```yaml
variables: []
availablePrevNodes: []
availableNextNodes: []
```

### 4.4 条件节点的值类型必须匹配
在if-else节点中，`value`的类型必须与`varType`一致：
```yaml
# 正确示例
value: "年轻单身型"
varType: string

# 错误示例
value: ["年轻单身型"]
varType: string
```

### 4.5 代码节点必须使用正确的语言标识
代码节点必须使用`code_language: python3`而非`language: python`：
```yaml
type: code
code_language: python3  # 正确
# language: python      # 错误
```

### 4.6 数组类型必须指定元素类型
输出定义中的数组类型必须指定元素类型：
```yaml
outputs:
  products:
    type: array[object]  # 正确
  # type: array          # 错误，未指定元素类型
```
