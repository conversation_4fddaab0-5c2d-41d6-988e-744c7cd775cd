# Dify工作流故障排除指南

## 错误类型1: Cannot read properties of undefined (reading 'filter')

### 错误信息
```
4348-133b570d0af8fa77.js:1 Uncaught TypeError: Cannot read properties of undefined (reading 'filter')
    at Object.checkValid (4348-133b570d0af8fa77.js:1:1176382)
    at 4348-133b570d0af8fa77.js:1:1228901
    at Object.ai [as useMemo] (965d0215-d661dd9fca8e5b15.js:1:47842)
    at t.useMemo (8418-76f95aa25de7d6e2.js:1:87767)
    at eu (4348-133b570d0af8fa77.js:1:1228013)
```

### 问题原因
前端代码尝试在节点定义中使用`filter`方法，但相关属性是`undefined`。主要是因为节点缺少必要的属性，如`variables`、`outputs`等。

### 解决方法
1. 为代码节点添加`variables`和`outputs`属性：
```yaml
variables: []
outputs:
  products:
    type: array
  total_premium:
    type: number
```

2. 为问题分类器节点添加`outputs`属性：
```yaml
outputs:
  class_name:
    type: string
  class_id:
    type: string
```

3. 为所有LLM节点添加`outputs`属性：
```yaml
outputs:
  text:
    type: string
```

## 错误类型2: Cannot read properties of undefined (reading 'availablePrevNodes')

### 错误信息
```
4348-133b570d0af8fa77.js:1 Uncaught TypeError: Cannot read properties of undefined (reading 'availablePrevNodes')
    at 4348-133b570d0af8fa77.js:1:301230
    at Object.useMemo (965d0215-d661dd9fca8e5b15.js:1:51498)
    at t.useMemo (8418-76f95aa25de7d6e2.js:1:87767)
    at m (4348-133b570d0af8fa77.js:1:301217)
```

### 问题原因
前端代码尝试访问节点的`availablePrevNodes`属性，但该属性在某些节点上是未定义的。此外，节点类型名称不一致也可能导致前端代码无法正确识别节点类型。

### 解决方法
1. 修复节点类型的不一致问题：
   - 将`targetType: question_classifier`改为`targetType: question-classifier`
   - 将`sourceType: question_classifier`改为`sourceType: question-classifier`

2. 为if-else节点添加`outputs`属性：
```yaml
outputs:
  case_id:
    type: string
```

3. 为所有节点添加`availablePrevNodes`和`availableNextNodes`属性：
```yaml
availablePrevNodes: []
availableNextNodes: []
```

## 错误类型3: e.split is not a function

### 错误信息
```
4348-133b570d0af8fa77.js:1 Uncaught TypeError: e.split is not a function
    at o (4348-133b570d0af8fa77.js:1:620452)
    at $ (4348-133b570d0af8fa77.js:1:33039)
    at lS (965d0215-d661dd9fca8e5b15.js:1:39325)
    at ot (965d0215-d661dd9fca8e5b15.js:1:64573)
    at ov (965d0215-d661dd9fca8e5b15.js:1:75638)
    at ic (965d0215-d661dd9fca8e5b15.js:1:112360)
```

### 问题原因
前端代码尝试在一个非字符串类型的变量上调用`split`方法。这通常发生在以下情况：

1. if-else节点的条件定义中，`value`被定义为数组，但`varType`是`string`，导致类型不匹配
2. 模板填充工具节点中，当传入的数据不是预期的字符串格式
3. 代码节点中，当对可能为非字符串的变量调用字符串方法
4. 当对象类型的数据被错误地当作字符串处理

### 解决方法

#### 对于if-else节点
将if-else节点的条件定义中的`value`从数组改为字符串，与`varType: string`保持一致：

修改前：
```yaml
value:
  - 年轻单身型
varType: string
```

修改后：
```yaml
value: 年轻单身型
varType: string
```

#### 对于模板填充节点
在代码中添加类型检查或转换：

```javascript
// 错误的代码
"children": (e ? e.split("\n") : [""]).map(e => ({...}))

// 修复方法1：添加类型检查
"children": (e && typeof e === 'string' ? e.split("\n") : [""]).map(e => ({...}))

// 修复方法2：强制转换为字符串
"children": (e ? String(e).split("\n") : [""]).map(e => ({...}))
```

#### 对于代码节点
在处理数据时，始终检查数据类型或进行适当的类型转换：

```python
def process_data(data):
  # 确保data是字符串
  if not isinstance(data, str):
    data = str(data) if data is not None else ""

  # 现在可以安全地使用字符串方法
  lines = data.split("\n")
  return lines
```

确保在工作流中传递数据时，保持数据类型的一致性，特别是在节点之间传递数据时。

## 错误类型4: 模型不可用

### 错误信息
当工作流中使用了在当前 Dify 实例中不可用的模型（如 gpt-4o）时，可能会出现模型不可用的错误。

### 问题原因
工作流配置中使用了当前 Dify 实例中未配置或不可用的 LLM 模型，如 gpt-4o。

### 解决方法
将工作流中所有使用不可用模型的节点替换为当前系统中可用的模型，例如：

修改前：
```yaml
model:
  provider: openai
  name: gpt-4o
  mode: chat
```

修改后：
```yaml
model:
  provider: langgenius/openai_api_compatible/openai_api_compatible
  name: glm-4-flashx
  mode: chat
```

## 错误类型5: 代码节点中的Python代码报错

### 错误信息
在代码节点中，Python代码可能会出现语法错误或运行时错误，如输入数据格式不正确、JSON解析失败等。

### 问题原因
1. 代码节点中的Python代码对输入数据的处理不够健壮
2. JSON解析逻辑不够灵活，无法处理各种格式的输入
3. 错误处理不完善，导致代码在异常情况下崩溃

### 解决方法
增强代码节点中的错误处理和输入验证：

```python
# 获取输入文本 - 增强版
# 处理多种可能的输入格式
if hasattr(input_data, 'text'):
    text = input_data.text
elif isinstance(input_data, dict) and 'text' in input_data:
    text = input_data['text']
elif isinstance(input_data, str):
    text = input_data
else:
    # 如果无法获取文本，返回默认值
    return {"error": "无法获取输入数据", "products": [], "total_premium": 0}

# 更灵活的JSON提取逻辑
try:
    # 尝试直接解析整个文本为JSON
    products_json = json.loads(text)
except:
    # 如果失败，尝试从文本中提取JSON部分
    json_start = text.find('```json')
    if json_start == -1:
        # 尝试其他可能的JSON标记
        json_start = text.find('{"products":')

    # 更多的错误处理逻辑...
```

## 错误类型6: 上下文变量访问错误

### 错误信息
工作流运行时出现“无法访问变量”的错误，或者节点无法获取其他节点的输出数据。

### 问题原因
1. 节点没有启用上下文功能（context: enabled: true）
2. 节点的variable_selector配置不正确，没有包含需要访问的变量
3. 节点之间的数据流动关系配置不正确

### 解决方法
对需要访问其他节点变量的节点，启用上下文功能并正确配置变量选择器：

```yaml
# 在节点配置中添加上下文配置
context:
  enabled: true  # 启用上下文功能
  variable_selector:  # 配置需要访问的变量
    - start_node  # 节点名称
    - age  # 变量名称
    - gender
    - income
    - analyze_customer  # 另一个节点
    - text  # 该节点的输出变量

# 在系统提示中添加上下文标记
prompt_template:
  - id: system-prompt
    role: system
    text: '{{#context#}}

      你是一位专业的顾问...'  # 在系统提示开头添加{{#context#}}标记
```

对于代码节点，可能需要使用input_mapping和output_mapping来映射数据：

```yaml
input_mapping:
  - source_selector:
      - match_products_young  # 源节点
      - text  # 源节点的变量
    target: text  # 目标变量名

output_mapping:
  - source: products  # 代码输出的变量
    target: products  # 映射到的节点变量
```

## 错误类型7: 代码节点验证错误

### 错误信息
工作流运行时出现代码节点验证错误，如：

```
validation errors for CodeNodeData code_language Field required
outputs.products.type Input should be 'string', 'number', 'object', 'array[string]', 'array[number]' or 'array[object]'
```

或者运行时出现语法错误：

```
SyntaxError: 'return' outside function
error: exit status 255
```

### 问题原因
1. 代码节点缺少必填字段 `code_language`，或使用了错误的字段名（如使用 `language` 而非 `code_language`）
2. 输出类型定义不正确，如使用了 `array` 而不是 `array[object]`、`array[string]` 等特定类型的数组
3. 代码节点的函数入口点不正确，如使用了 `process` 而不是 `main`
4. 在代码中使用了全局范围的 `return` 语句，而不是在 `main` 函数内部返回结果

### 解决方法
按照正确的格式配置代码节点：

```yaml
# 正确的代码节点配置
type: code
title: 代码节点名称
code_language: python3  # 必填字段，指定代码语言

# 定义输出变量及其类型
outputs:
  products:  # 输出变量名
    type: array[object]  # 必须指定数组元素类型，而不是仅仅用 array
  total_premium:
    type: number

# 定义输入变量
variables:
  - value_selector:  # 使用 value_selector 而非 source_selector
      - match_products_young  # 源节点
      - text  # 源节点的变量
    variable: input_data  # 使用 variable 而非 target

# 代码内容
code: |
  import json
  import re

  def main(input_data) -> dict:  # 使用 main 函数作为入口点，而不是 process
      # 代码逻辑...
      return {
          "products": products,
          "total_premium": total_premium
      }
```

**重要提示**：在Dify的代码节点中，所有代码必须包含在`main`函数内部，并且通过`return`语句返回结果。不要在全局范围内使用`return`语句，这会导致语法错误。

错误示例：
```python
def process(text):
    # 处理逻辑...
    return result

# 错误：在全局范围内使用return
result = process(input_data)
return result  # 这会导致SyntaxError: 'return' outside function
```

正确示例：
```python
def process(text):
    # 处理逻辑...
    return result

def main(input_data):
    # 调用处理函数
    return process(input_data)  # 在main函数内部返回结果
```

## 错误类型8: Cannot read properties of undefined (reading 'filter')

### 错误信息
```
Uncaught TypeError: Cannot read properties of undefined (reading 'filter')
    at Object.checkValid (4348-133b570d0af8fa77.js:1:1176382)
    at 4348-133b570d0af8fa77.js:1:1228901
    at Object.ai [as useMemo] (965d0215-d661dd9fca8e5b15.js:1:47842)
    at t.useMemo (8418-76f95aa25de7d6e2.js:1:87767)
    at eu (4348-133b570d0af8fa77.js:1:1228013)
```

### 问题原因
这个错误可能有两种情况：

1. 在LLM节点的上下文配置中，`variable_selector`数组中缺少必要的变量，导致前端代码在尝试过滤变量时出错。
2. 在代码节点中，`variables`属性未定义或为空数组，但代码中引用了其他节点的变量。

### 解决方法

#### 对于LLM节点
确保LLM节点的上下文配置中包含所有需要的变量：

```yaml
context:
  enabled: true
  variable_selector:
  - 'start-node'  # 起始节点
  - files  # 文件变量
  - tone  # 语气变量
  - lang  # 语言变量
  - length  # 长度变量
  - format  # 格式变量
```

确保所有在LLM提示模板中使用的变量都在`variable_selector`中列出。

#### 对于代码节点
确保代码节点的`variables`属性正确定义，包含所有需要的变量：

```yaml
variables:
- variable: "text"  # 变量名称
  value_selector:  # 变量来源
  - "source-node-id"  # 源节点ID
  - "source-variable-name"  # 源变量名称
```

每个变量定义必须包含`variable`属性和非空的`value_selector`数组。`value_selector`数组的第一个元素是源节点ID，第二个元素是源变量名称。

## 错误类型9: Output X is missing

### 错误信息
```
Run failed: Output replacements is missing.
```

### 问题原因
代码节点或其他节点未能输出下游节点所需的变量。这通常发生在以下情况：

1. 代码节点中的异常处理路径没有返回所有必要的输出变量
2. 代码节点中的条件分支只在某些情况下返回特定变量
3. 输入数据不完整或格式不正确，导致处理逻辑无法生成所有必要的输出

### 解决方法
确保代码节点在所有可能的执行路径中都返回所有声明的输出变量，即使是空值或默认值：

```python
def main(text):
    try:
        # 处理逻辑...
        result = process(text)

        # 确保所有必要的输出字段都存在
        if "replacements" not in result or not result["replacements"]:
            result["replacements"] = "{}"
        if "template_structure" not in result or not result["template_structure"]:
            result["template_structure"] = "{}"
        if "result" not in result:
            result["result"] = "处理完成"

        return result
    except Exception as e:
        # 即使发生异常，也返回所有必要的输出变量
        return {
            "result": "处理失败",
            "error": str(e),
            "replacements": "{}",
            "template_structure": "{}",
            "original_text": text
        }
```

在异常处理和条件分支中，确保返回与`outputs`配置中定义的所有变量。对于可能为空的JSON字段，返回空的JSON对象（`{}`）或数组（`[]`）而不是`null`或空字符串。

## 错误类型10: 大模型输出长度限制导致JSON截断

### 错误信息
```
Run failed: Output replacements is missing.
```
或者JSON解析错误：
```
Unexpected end of JSON input
```

### 问题原因
大模型（如GPT-4、Moonshot等）有输出长度限制，当要求模型生成的JSON结构过大时，可能会被截断，导致JSON不完整或缺少必要字段。

### 解决方法
有几种方法可以解决这个问题：

#### 方法1：动态提取模板占位符（推荐）
1. 使用代码节点1解析模板文本，提取所有占位符
2. 修改LLM节点提示词，只要求生成填充内容（replaceJson）
3. 代码节点2处理LLM输出，确保所有占位符都有对应的填充内容

```python
# 代码节点1：提取占位符
def main(template_text):
    # 提取所有的占位符，格式为[xxx]
    placeholder_pattern = r'\[(.*?)\]'
    placeholders = re.findall(placeholder_pattern, template_text)

    # 去重，因为同一个占位符可能在文档中出现多次
    unique_placeholders = list(set(placeholders))

    # 构建templateJson结构
    template_json = {}
    for placeholder in unique_placeholders:
        placeholder_with_brackets = f"[{placeholder}]"
        template_json[placeholder_with_brackets] = {
            "type": 1,
            "description": f"填写{placeholder}"
        }

    # 构建占位符列表，用于LLM提示词
    placeholders_formatted = []
    for placeholder in unique_placeholders:
        placeholders_formatted.append(f"- [{placeholder}]：填写{placeholder}")

    return {
        "template_structure": json.dumps(template_json, ensure_ascii=False),
        "placeholders_list": "\n".join(placeholders_formatted)
    }

# 代码节点2：处理LLM输出
def main(text, template_structure):
    try:
        # 解析templateJson
        template_json = json.loads(template_structure)

        # 从LLM输出中提取replaceJson
        replace_json = extract_replace_json(text)

        # 确保所有模板字段在replaceJson中都有对应的值
        for key in template_json.keys():
            if key not in replace_json:
                replace_json[key] = f"请填写{template_json[key]['description']}"

        return {
            "template_structure": template_structure,
            "replacements": json.dumps(replace_json, ensure_ascii=False)
        }
    except Exception as e:
        # 异常处理...
```

#### 方法2：优化提示词，减少输出长度
1. 明确指示模型只输出必要的JSON结构，不要添加额外解释
2. 对长文本字段要求模型进行摘要或限制字数
3. 使用更紧凑的JSON结构，减少嵌套层级和冗余字段

#### 方法3：增强代码节点的错误处理和修复能力
1. 实现更健壮的JSON解析方法，尝试修复常见的JSON格式错误
2. 实现部分解析功能，即使JSON不完整也能提取可用的部分
3. 对于缺失的字段，提供默认值或从上下文中推断

#### 方法4：分块生成和合并
1. 将大型JSON结构拆分成多个小块，分别生成后再合并
2. 修改工作流，添加多个LLM节点，每个节点负责生成JSON的一部分
3. 使用代码节点将这些部分合并成完整的JSON结构

**注意**：使用多个节点分别生成JSON的不同部分可能会导致一致性问题，建议使用方法1或方法2。

## 错误类型11: i.replace is not a function

### 错误信息
```
Uncaught TypeError: i.replace is not a function
    at o (4348-133b570d0af8fa77.js:1:620452)
    at $ (4348-133b570d0af8fa77.js:1:33039)
    at lS (965d0215-d661dd9fca8e5b15.js:1:39325)
```

### 问题原因
这个错误与"e.split is not a function"类似，但发生在尝试对非字符串类型的值调用`replace`方法时。在Dify工作流中，这通常发生在以下情况：

1. 模板填充工具节点中，当传入的数据不是预期的字符串格式
2. 代码节点中，当对可能为非字符串的变量调用字符串方法
3. 当对象或数组类型的数据被错误地当作字符串处理

### 解决方法

#### 对于模板填充节点
在代码中添加类型检查或转换：

```javascript
// 错误的代码
const result = i.replace(/\[(\w+)\]/g, function(match, key) {...});

// 修复方法1：添加类型检查
const result = (typeof i === 'string' ? i.replace(/\[(\w+)\]/g, function(match, key) {...}) : i);

// 修复方法2：强制转换为字符串
const result = String(i).replace(/\[(\w+)\]/g, function(match, key) {...});
```

#### 对于代码节点
在处理数据时，始终检查数据类型或进行适当的类型转换：

```python
def process_template(template_data):
    # 确保template_data是字符串
    if not isinstance(template_data, str):
        template_data = str(template_data) if template_data is not None else ""

    # 现在可以安全地使用字符串方法
    result = template_data.replace("[name]", "张三")
    return result
```

#### 对于JSON数据处理
当处理可能是JSON字符串或对象的数据时，先检查类型再处理：

```python
def process_json_data(data):
    # 如果是字符串，尝试解析为JSON对象
    if isinstance(data, str):
        try:
            json_data = json.loads(data)
        except:
            json_data = {}
    # 如果已经是字典，直接使用
    elif isinstance(data, dict):
        json_data = data
    else:
        json_data = {}

    # 处理JSON数据...
    return json_data
```

#### 对于当前错误的具体解决方案
针对当前遇到的 `e.split is not a function` 错误，可以在代码节点中添加以下代码来处理模板填充工具的输入：

```python
def main(template_text) -> dict:
    """处理模板文本并提取占位符"""
    # 确保template_text是字符串
    if not isinstance(template_text, str):
        if template_text is None:
            template_text = ""
        else:
            try:
                # 尝试将非字符串类型转换为字符串
                template_text = str(template_text)
            except:
                template_text = ""

    # 提取所有的占位符，格式为[xxx]
    placeholder_pattern = r'\[(.*?)\]'
    placeholders = re.findall(placeholder_pattern, template_text)

    # 去重，因为同一个占位符可能在文档中出现多次
    unique_placeholders = list(set(placeholders))

    # 构建templateJson结构
    template_json = {}
    for placeholder in unique_placeholders:
        placeholder_with_brackets = f"[{placeholder}]"
        template_json[placeholder_with_brackets] = {
            "type": 1,
            "description": f"填写{placeholder}"
        }

    # 构建占位符列表，用于LLM提示词
    placeholders_formatted = []
    for placeholder in unique_placeholders:
        placeholders_formatted.append(f"- [{placeholder}]：填写{placeholder}")

    return {
        "template_structure": json.dumps(template_json, ensure_ascii=False),
        "placeholders_list": "\n".join(placeholders_formatted),
        "placeholders_count": len(unique_placeholders),
        "result": f"成功提取{len(unique_placeholders)}个占位符"
    }
```

如果错误发生在模板填充工具节点中，可以修改工作流配置，在使用docx-template工具之前添加一个代码节点，确保传递给工具的数据是正确的字符串格式：

```python
def main(replacements, template_structure) -> dict:
    """确保传递给模板填充工具的数据是正确的字符串格式"""
    # 处理replacements
    if not isinstance(replacements, str):
        if isinstance(replacements, dict):
            replacements = json.dumps(replacements, ensure_ascii=False)
        elif replacements is None:
            replacements = "{}"
        else:
            try:
                replacements = str(replacements)
            except:
                replacements = "{}"

    # 处理template_structure
    if not isinstance(template_structure, str):
        if isinstance(template_structure, dict):
            template_structure = json.dumps(template_structure, ensure_ascii=False)
        elif template_structure is None:
            template_structure = "{}"
        else:
            try:
                template_structure = str(template_structure)
            except:
                template_structure = "{}"

    return {
        "replacements": replacements,
        "template_structure": template_structure
    }
```

## 一般性建议

在创建或修改Dify工作流YAML文件时，请确保：

1. 所有节点都有必要的属性，如`variables`、`outputs`等
2. 节点类型名称保持一致，特别是在`sourceType`和`targetType`中
3. 为所有节点添加`availablePrevNodes`和`availableNextNodes`属性，即使它们是空数组
4. 确保所有节点之间的连接关系正确定义
5. 当`varType`为`string`时，确保相应的`value`也是字符串而不是数组
6. 使用当前 Dify 实例中可用的 LLM 模型，如 glm-4-flashx，而不是使用可能不可用的模型（如 gpt-4o）
7. 在代码节点中添加充分的错误处理和输入验证，以处理各种异常情况
8. 对需要访问其他节点变量的节点，启用上下文功能（context: enabled: true），正确配置 variable_selector，并在系统提示中添加 {{#context#}} 标记
9. 确保LLM节点的上下文配置中包含所有在提示模板中使用的变量
10. 在使用变量选择器时，确保所有引用的变量都存在于相应的节点中
11. 确保代码节点在所有执行路径中都返回所有声明的输出变量，即使是空值或默认值
12. 对于需要大模型生成复杂JSON结构的场景，考虑使用预定义模板和字段映射方法，避免因输出长度限制导致的JSON截断问题
13. 在处理字符串操作时，始终检查变量类型，确保在调用字符串方法（如`split`、`replace`等）前，变量确实是字符串类型
14. 在代码节点中处理来自其他节点的数据时，不要假设数据类型，始终进行类型检查和适当的转换

如果遇到前端错误，请检查浏览器控制台中的错误信息，特别是关于"undefined"属性的错误，这通常表示YAML文件中缺少前端代码期望的某些属性。
