kind: app
version: "0.1.5"
app:
  mode: workflow
  name: "Dify工作流生成器"
  description: "根据场景描述自动生成Dify工作流DSL"
  icon: "🔄"
  icon_background: "#E6F7FF"
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: package
  value:
    plugin_unique_identifier: langgenius/openai_api_compatible:0.1.11@aeb88e81bb79db614de257ad66eddde90cede8a3917c986fbd2be46df84d2e23
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        sourceType: start
        targetType: llm
      id: start_node-source-analyze_scenario-target
      source: start_node
      sourceHandle: source
      target: analyze_scenario
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: code
      id: analyze_scenario-source-extract_scenario_data-target
      source: analyze_scenario
      sourceHandle: source
      target: extract_scenario_data
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: if-else
      id: extract_scenario_data-source-check_valid-target
      source: extract_scenario_data
      sourceHandle: source
      target: check_valid
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: llm
      id: check_valid-source-handle_error-target
      source: check_valid
      sourceHandle: '2'
      target: handle_error
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: llm
      id: check_valid-source-plan_nodes-target
      source: check_valid
      sourceHandle: '1'
      target: plan_nodes
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: llm
      id: plan_nodes-source-generate_dsl-target
      source: plan_nodes
      sourceHandle: source
      target: generate_dsl
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: llm
      id: generate_dsl-source-format_result-target
      source: generate_dsl
      sourceHandle: source
      target: format_result
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: end
      id: format_result-source-end_node-target
      source: format_result
      sourceHandle: source
      target: end_node
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        availableNextNodes: []
        availablePrevNodes: []
        selected: true
        title: "开始节点"
        type: start
        variables:
        - label: "场景描述"
          max_length: 2000
          required: true
          type: paragraph
          variable: scenario_description
        vision:
          enabled: false
      height: 90
      id: start_node
      position:
        x: 80
        y: 282
      positionAbsolute:
        x: 80
        y: 282
      selected: true
      type: custom
      width: 244
    
    - data:
        availableNextNodes: []
        availablePrevNodes: []
        context:
          enabled: true
          variable_selector:
          - start_node
          - scenario_description
        model:
          completion_params:
            temperature: 0.5
          mode: chat
          name: glm-4-flashx
          provider: langgenius/openai_api_compatible/openai_api_compatible
        outputs:
          text:
            type: string
        prompt_template:
        - id: system-prompt
          role: system
          text: '{{#context#}}
            你是一个专业的Dify工作流分析专家。你的任务是分析用户提供的场景描述，提取关键信息，为后续生成Dify工作流DSL做准备。
            
            请分析用户提供的场景描述，提取以下信息：
            1. 工作流名称和描述
            2. 输入内容和类型
            3. 输出内容和类型
            4. 流程步骤及其对应的节点类型
            5. 节点之间的连接关系
            
            请以JSON格式返回分析结果，包含以下字段：
            ```json
            {
              "workflow_name": "工作流名称",
              "workflow_description": "工作流描述",
              "inputs": [{"name": "输入名称", "type": "输入类型"}],
              "outputs": [{"name": "输出名称", "type": "输出类型"}],
              "steps": [
                {
                  "id": "步骤ID",
                  "title": "步骤标题",
                  "type": "节点类型(start/llm/code/if-else等)",
                  "description": "步骤描述",
                  "inputs": ["输入1", "输入2"],
                  "outputs": ["输出1", "输出2"]
                }
              ],
              "connections": [
                {
                  "source": "源节点ID",
                  "target": "目标节点ID"
                }
              ]
            }
            ```
            
            注意：
            - 节点类型应该是Dify支持的类型：start, llm, code, if-else, question-classifier, tool, knowledge等
            - 确保每个步骤都有唯一的ID
            - 确保连接关系正确反映流程步骤的顺序'
        - id: human-prompt
          role: human
          text: '请分析以下场景描述：

{{scenario_description}}'
        selected: false
        title: "分析场景描述"
        type: llm
        vision:
          enabled: false
      height: 90
      id: analyze_scenario
      position:
        x: 380
        y: 282
      positionAbsolute:
        x: 380
        y: 282
      selected: false
      type: custom
      width: 244
    
    - data:
        availableNextNodes: []
        availablePrevNodes: []
        code: |
          import json
          import re
          
          def main(input_data) -> dict:
              """从LLM输出中提取场景数据"""
              try:
                  # 尝试从LLM输出中提取JSON
                  json_match = re.search(r'```json\s*(.*?)\s*```', input_data, re.DOTALL)
                  if json_match:
                      data = json.loads(json_match.group(1))
                  else:
                      # 尝试直接解析整个文本
                      try:
                          data = json.loads(input_data)
                      except:
                          # 如果解析失败，返回错误信息
                          return {
                              "error": "无法解析场景分析结果",
                              "valid": False,
                              "workflow_name": "未命名工作流",
                              "workflow_description": "未提供描述",
                              "steps_count": 0,
                              "connections_count": 0
                          }
                  
                  # 验证数据完整性
                  required_fields = ["workflow_name", "workflow_description", "steps", "connections"]
                  for field in required_fields:
                      if field not in data:
                          return {
                              "error": f"缺少必要字段: {field}",
                              "valid": False,
                              "workflow_name": data.get("workflow_name", "未命名工作流"),
                              "workflow_description": data.get("workflow_description", "未提供描述"),
                              "steps_count": len(data.get("steps", [])),
                              "connections_count": len(data.get("connections", []))
                          }
                  
                  # 确保每个步骤都有唯一ID
                  step_ids = [step.get("id") for step in data.get("steps", [])]
                  if len(step_ids) != len(set(step_ids)):
                      return {
                          "error": "存在重复的步骤ID",
                          "valid": False,
                          "workflow_name": data.get("workflow_name", "未命名工作流"),
                          "workflow_description": data.get("workflow_description", "未提供描述"),
                          "steps_count": len(data.get("steps", [])),
                          "connections_count": len(data.get("connections", []))
                      }
                  
                  # 返回验证通过的数据
                  return {
                      "valid": True,
                      "workflow_name": data.get("workflow_name", "未命名工作流"),
                      "workflow_description": data.get("workflow_description", "未提供描述"),
                      "steps_count": len(data.get("steps", [])),
                      "connections_count": len(data.get("connections", [])),
                      "scenario_data": json.dumps(data)  # 将对象转换为JSON字符串
                  }
              except Exception as e:
                  # 捕获所有异常
                  return {
                      "error": f"处理场景数据时出错: {str(e)}",
                      "valid": False,
                      "workflow_name": "未命名工作流",
                      "workflow_description": "未提供描述",
                      "steps_count": 0,
                      "connections_count": 0
                  }
        code_language: python3
        outputs:
          valid:
            type: boolean
          error:
            type: string
          workflow_name:
            type: string
          workflow_description:
            type: string
          steps_count:
            type: number
          connections_count:
            type: number
          scenario_data:
            type: string
        selected: false
        title: "提取场景数据"
        type: code
        variables: []
        vision:
          enabled: false
      height: 90
      id: extract_scenario_data
      position:
        x: 680
        y: 282
      positionAbsolute:
        x: 680
        y: 282
      selected: false
      type: custom
      width: 244
    
    - data:
        availableNextNodes: []
        availablePrevNodes: []
        cases:
        - case_id: '1'
          conditions:
          - comparison_operator: equal
            id: condition1
            value: true
            varType: boolean
            variable_selector:
            - extract_scenario_data
            - valid
          id: '1'
          logical_operator: and
        - case_id: '2'
          conditions:
          - comparison_operator: equal
            id: condition2
            value: false
            varType: boolean
            variable_selector:
            - extract_scenario_data
            - valid
          id: '2'
          logical_operator: and
        outputs:
          case_id:
            type: string
        selected: false
        title: "检查有效性"
        type: if-else
        vision:
          enabled: false
      height: 90
      id: check_valid
      position:
        x: 980
        y: 282
      positionAbsolute:
        x: 980
        y: 282
      selected: false
      type: custom
      width: 244
    
    - data:
        availableNextNodes: []
        availablePrevNodes: []
        context:
          enabled: true
          variable_selector:
          - extract_scenario_data
          - error
        model:
          completion_params:
            temperature: 0.5
          mode: chat
          name: glm-4-flashx
          provider: langgenius/openai_api_compatible/openai_api_compatible
        outputs:
          text:
            type: string
        prompt_template:
        - id: system-prompt
          role: system
          text: '{{#context#}}
            你是一个专业的Dify工作流助手。在分析场景描述时遇到了错误，请向用户解释错误并提供改进建议。'
        - id: human-prompt
          role: human
          text: '在分析您提供的场景描述时遇到了以下错误：

错误信息: {{error}}

请尝试提供更详细的场景描述，包括：
1. 清晰的【输入】部分：描述工作流接收的输入内容
2. 明确的【输出】部分：描述工作流产生的输出内容
3. 详细的【流程】部分：按步骤描述工作流的处理过程，从"开始"到"结束"

例如：
【输入】：用户通过文本提出的问题
【输出】：针对用户问题的回答
【流程】：
1、开始
2、接收用户问题
3、分析问题类型
4、生成回答
5、结束

请重新提供更完整的场景描述，我将帮您生成相应的Dify工作流。'
        selected: false
        title: "处理错误"
        type: llm
        vision:
          enabled: false
      height: 90
      id: handle_error
      position:
        x: 1280
        y: 182
      positionAbsolute:
        x: 1280
        y: 182
      selected: false
      type: custom
      width: 244
    
    - data:
        availableNextNodes: []
        availablePrevNodes: []
        context:
          enabled: true
          variable_selector:
          - extract_scenario_data
          - workflow_name
          - extract_scenario_data
          - workflow_description
          - extract_scenario_data
          - steps_count
          - extract_scenario_data
          - connections_count
          - extract_scenario_data
          - scenario_data
        model:
          completion_params:
            temperature: 0.5
          mode: chat
          name: glm-4-flashx
          provider: langgenius/openai_api_compatible/openai_api_compatible
        outputs:
          text:
            type: string
        prompt_template:
        - id: system-prompt
          role: system
          text: '{{#context#}}
            你是一个专业的Dify工作流设计专家。你的任务是根据场景分析结果，规划详细的Dify工作流节点配置。
            
            请根据提供的场景数据，为每个步骤设计详细的节点配置，包括：
            1. 节点类型和属性
            2. 输入和输出变量
            3. 节点之间的连接关系
            4. 对于LLM节点，设计适当的提示词
            5. 对于代码节点，设计基本的代码逻辑
            
            请以JSON格式返回规划结果，包含以下结构：
            ```json
            {
              "nodes": {
                "node_id": {
                  "type": "节点类型",
                  "title": "节点标题",
                  "config": {
                    // 节点特定配置
                  },
                  "variables": [],
                  "outputs": {
                    "output_name": {
                      "type": "output_type"
                    }
                  },
                  "availablePrevNodes": [],
                  "availableNextNodes": []
                }
              },
              "edges": [
                {
                  "source": "源节点ID",
                  "target": "目标节点ID",
                  "sourceHandle": "",
                  "targetHandle": "",
                  "sourceType": "源节点类型",
                  "targetType": "目标节点类型"
                }
              ]
            }
            ```
            
            注意：
            - 确保包含开始节点(start_node)
            - 为每个节点添加必要的属性和配置
            - 确保节点之间的连接关系正确
            - 对于LLM节点，使用glm-4-flashx模型
            - 为所有节点添加availablePrevNodes和availableNextNodes属性(即使为空)
            - 确保所有节点都有variables和outputs属性
            - 对于代码节点，使用code_language: python3
            - 对于数组类型，使用array[object]、array[string]或array[number]格式'
        - id: human-prompt
          role: human
          text: '请根据以下场景数据规划Dify工作流节点：

工作流名称: {{workflow_name}}
工作流描述: {{workflow_description}}
步骤数量: {{steps_count}}
连接数量: {{connections_count}}

场景数据:
{{scenario_data}}'
        selected: false
        title: "规划节点"
        type: llm
        vision:
          enabled: false
      height: 90
      id: plan_nodes
      position:
        x: 1280
        y: 382
      positionAbsolute:
        x: 1280
        y: 382
      selected: false
      type: custom
      width: 244
    
    - data:
        availableNextNodes: []
        availablePrevNodes: []
        context:
          enabled: true
          variable_selector:
          - extract_scenario_data
          - workflow_name
          - extract_scenario_data
          - workflow_description
          - plan_nodes
          - text
        model:
          completion_params:
            temperature: 0.5
          mode: chat
          name: glm-4-flashx
          provider: langgenius/openai_api_compatible/openai_api_compatible
        outputs:
          text:
            type: string
        prompt_template:
        - id: system-prompt
          role: system
          text: '{{#context#}}
            你是一个专业的Dify工作流DSL生成专家。你的任务是根据节点规划结果，生成完整的Dify工作流DSL文件内容。
            
            请注意以下重要事项：
            1. DSL文件必须以kind: app、version: "0.1.5"和app配置开头
            2. 必须将app.mode设置为"workflow"
            3. 为所有节点添加必要的属性，如variables和outputs
            4. 确保节点类型名称一致，特别是在sourceType和targetType中
            5. 当varType为string时，确保相应的value也是字符串而不是数组
            6. 使用code_language: python3而非language: python
            7. 正确指定数组类型：array[object]、array[string]或array[number]
            8. 对需要访问其他节点变量的节点，启用上下文功能
            
            请生成完整的YAML格式DSL文件，确保格式正确，可以直接在Dify平台上使用。
            
            DSL文件必须包含以下结构：
            ```yaml
            kind: app
            version: "0.1.5"
            app:
              mode: workflow
              name: "工作流名称"
              description: "工作流描述"
              icon: "图标"
              icon_background: "#颜色代码"
              use_icon_as_answer_icon: false
            dependencies:
            - current_identifier: null
              type: package
              value:
                plugin_unique_identifier: langgenius/openai_api_compatible:0.1.11@aeb88e81bb79db614de257ad66eddde90cede8a3917c986fbd2be46df84d2e23
            workflow:
              conversation_variables: []
              environment_variables: []
              features:
                # 特性配置...
              graph:
                edges:
                  # 边定义...
                nodes:
                  # 节点定义...
                viewport:
                  x: 0
                  y: 0
                  zoom: 1
            ```'
        - id: human-prompt
          role: human
          text: '请根据以下节点规划生成完整的Dify工作流DSL：

工作流名称: {{workflow_name}}
工作流描述: {{workflow_description}}

节点规划:
{{text}}'
        selected: false
        title: "生成DSL"
        type: llm
        vision:
          enabled: false
      height: 90
      id: generate_dsl
      position:
        x: 1580
        y: 282
      positionAbsolute:
        x: 1580
        y: 282
      selected: false
      type: custom
      width: 244
    
    - data:
        availableNextNodes: []
        availablePrevNodes: []
        context:
          enabled: true
          variable_selector:
          - extract_scenario_data
          - workflow_name
          - generate_dsl
          - text
        model:
          completion_params:
            temperature: 0.5
          mode: chat
          name: glm-4-flashx
          provider: langgenius/openai_api_compatible/openai_api_compatible
        outputs:
          text:
            type: string
        prompt_template:
        - id: system-prompt
          role: system
          text: '{{#context#}}
            你是一个专业的Dify工作流助手。你的任务是向用户展示生成的工作流DSL，并提供使用说明。
            
            请按照以下格式回复用户：
            
            ## 🎉 工作流生成成功
            
            已为您生成"{{workflow_name}}"工作流的DSL文件。
            
            ### 📝 使用说明
            
            1. 复制下方的YAML代码
            2. 在Dify平台创建新的工作流应用
            3. 选择"导入DSL"选项
            4. 粘贴YAML代码并导入
            
            ### 📄 DSL文件内容
            
            ```yaml
            {{text}}
            ```
            
            ### ⚠️ 注意事项
            
            - 导入后请检查工作流配置，确保所有节点和连接正确
            - 可能需要根据实际需求调整LLM节点的提示词
            - 对于代码节点，可能需要完善代码逻辑
            
            如需修改或优化工作流，请提供更详细的需求描述。'
        - id: human-prompt
          role: human
          text: '请格式化工作流生成结果。'
        selected: false
        title: "格式化结果"
        type: llm
        vision:
          enabled: false
      height: 90
      id: format_result
      position:
        x: 1880
        y: 282
      positionAbsolute:
        x: 1880
        y: 282
      selected: false
      type: custom
      width: 244
    
    - data:
        availableNextNodes: []
        availablePrevNodes: []
        outputs:
        - value_selector:
          - format_result
          - text
          variable: workflow_dsl
        selected: false
        title: "结束"
        type: end
        vision:
          enabled: false
      height: 90
      id: end_node
      position:
        x: 2180
        y: 282
      positionAbsolute:
        x: 2180
        y: 282
      selected: false
      type: custom
      width: 244
    viewport:
      x: 0
      y: 0
      zoom: 1
