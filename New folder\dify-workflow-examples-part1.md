# Dify工作流示例 - 第一部分：简单工作流

本文档提供了一些完整的Dify工作流示例，从简单到复杂，可以作为创建自己的工作流的参考。

## 1. 简单问答工作流

这是一个基本的问答工作流，包含开始节点和LLM节点。

### 用途
适用于简单的问答场景，如FAQ回答、信息查询等。

### 完整DSL
```yaml
kind: app
version: "0.1.5"
app:
  mode: workflow
  name: "简单问答助手"
  description: "回答用户提出的问题"
  icon: "🤔"
  icon_background: "#FFEEDD"
  use_icon_as_answer_icon: false
  
  nodes:
    start_node:
      type: start
      title: "开始节点"
      variables: []
      outputs:
        text:
          type: string
      availablePrevNodes: []
      availableNextNodes: []
    
    answer_question:
      type: llm
      title: "回答问题"
      model:
        provider: langgenius/openai_api_compatible/openai_api_compatible
        name: glm-4-flashx
        mode: chat
      prompt_template:
        - id: system-prompt
          role: system
          text: '{{#context#}}
            你是一个知识渊博、乐于助人的助手。你的任务是回答用户的问题，提供准确、有用的信息。
            回答时请遵循以下原则：
            1. 保持回答简洁明了
            2. 如果不确定，请坦诚表明
            3. 避免提供错误信息
            4. 使用礼貌、专业的语气'
        - id: human-prompt
          role: human
          text: '{{text}}'
      variables: []
      outputs:
        text:
          type: string
      context:
        enabled: true
        variable_selector:
          - start_node
          - text
      availablePrevNodes: []
      availableNextNodes: []
  
  edges:
    - source: start_node
      target: answer_question
      sourceHandle: ""
      targetHandle: ""
      sourceType: start
      targetType: llm
```

## 2. 文本分类工作流

这个工作流使用问题分类器节点对输入文本进行分类，然后根据分类结果选择不同的处理路径。

### 用途
适用于需要根据输入类型提供不同回答的场景，如客服分流、内容分类等。

### 完整DSL
```yaml
kind: app
version: "0.1.5"
app:
  mode: workflow
  name: "文本分类助手"
  description: "根据输入类型提供不同的回答"
  icon: "📋"
  icon_background: "#E6F7FF"
  use_icon_as_answer_icon: false
  
  nodes:
    start_node:
      type: start
      title: "开始节点"
      variables: []
      outputs:
        text:
          type: string
      availablePrevNodes: []
      availableNextNodes: []
    
    classify_input:
      type: question-classifier
      title: "输入分类"
      variables: []
      outputs:
        class_name:
          type: string
        class_id:
          type: string
      availablePrevNodes: []
      availableNextNodes: []
    
    check_category:
      type: if-else
      title: "检查类别"
      variable: class_name
      operator: equal
      value: "问题"
      varType: string
      variables: []
      outputs:
        case_id:
          type: string
      availablePrevNodes: []
      availableNextNodes: []
    
    answer_question:
      type: llm
      title: "回答问题"
      model:
        provider: langgenius/openai_api_compatible/openai_api_compatible
        name: glm-4-flashx
        mode: chat
      prompt_template:
        - id: system-prompt
          role: system
          text: '{{#context#}}
            你是一个专业的问题解答助手。请回答用户的问题，提供详细、准确的信息。'
        - id: human-prompt
          role: human
          text: '{{text}}'
      variables: []
      outputs:
        text:
          type: string
      context:
        enabled: true
        variable_selector:
          - start_node
          - text
      availablePrevNodes: []
      availableNextNodes: []
    
    process_request:
      type: llm
      title: "处理请求"
      model:
        provider: langgenius/openai_api_compatible/openai_api_compatible
        name: glm-4-flashx
        mode: chat
      prompt_template:
        - id: system-prompt
          role: system
          text: '{{#context#}}
            你是一个专业的请求处理助手。用户提出了一个请求而非问题，请帮助用户完成请求或提供相关指导。'
        - id: human-prompt
          role: human
          text: '{{text}}'
      variables: []
      outputs:
        text:
          type: string
      context:
        enabled: true
        variable_selector:
          - start_node
          - text
      availablePrevNodes: []
      availableNextNodes: []
  
  edges:
    - source: start_node
      target: classify_input
      sourceHandle: ""
      targetHandle: ""
      sourceType: start
      targetType: question-classifier
    
    - source: classify_input
      target: check_category
      sourceHandle: ""
      targetHandle: ""
      sourceType: question-classifier
      targetType: if-else
    
    - source: check_category
      target: answer_question
      sourceHandle: ""
      targetHandle: ""
      sourceType: if-else
      targetType: llm
    
    - source: check_category
      target: process_request
      sourceHandle: ""
      targetHandle: ""
      sourceType: if-else
      targetType: llm
```

## 3. 数据提取工作流

这个工作流使用LLM节点从文本中提取结构化数据，然后使用代码节点进行处理。

### 用途
适用于需要从非结构化文本中提取信息的场景，如表单处理、简历分析等。

### 完整DSL
```yaml
kind: app
version: "0.1.5"
app:
  mode: workflow
  name: "数据提取助手"
  description: "从文本中提取结构化数据"
  icon: "📊"
  icon_background: "#F0F4FF"
  use_icon_as_answer_icon: false
  
  nodes:
    start_node:
      type: start
      title: "开始节点"
      variables: []
      outputs:
        text:
          type: string
      availablePrevNodes: []
      availableNextNodes: []
    
    extract_data:
      type: llm
      title: "提取数据"
      model:
        provider: langgenius/openai_api_compatible/openai_api_compatible
        name: glm-4-flashx
        mode: chat
      prompt_template:
        - id: system-prompt
          role: system
          text: '{{#context#}}
            你是一个专业的数据提取助手。你的任务是从用户提供的文本中提取结构化信息。
            请以JSON格式返回提取的数据，包含以下字段：
            - name: 姓名
            - age: 年龄
            - location: 位置
            - occupation: 职业
            - interests: 兴趣爱好(数组)
            如果某个字段在文本中未提及，请将其值设为null。'
        - id: human-prompt
          role: human
          text: '请从以下文本中提取信息：{{text}}'
      variables: []
      outputs:
        text:
          type: string
      context:
        enabled: true
        variable_selector:
          - start_node
          - text
      availablePrevNodes: []
      availableNextNodes: []
    
    process_data:
      type: code
      title: "处理数据"
      code_language: python3
      code: |
        import json
        import re
        
        def main(input_data) -> dict:
          """处理LLM提取的数据"""
          try:
            # 尝试从LLM输出中提取JSON
            json_match = re.search(r'```json\s*(.*?)\s*```', input_data, re.DOTALL)
            if json_match:
              data = json.loads(json_match.group(1))
            else:
              # 尝试直接解析整个文本
              try:
                data = json.loads(input_data)
              except:
                return {
                  "formatted_data": "无法解析提取的数据",
                  "valid": False
                }
            
            # 验证数据完整性
            required_fields = ["name", "age", "location", "occupation", "interests"]
            for field in required_fields:
              if field not in data:
                data[field] = None
            
            # 格式化输出
            formatted_data = f"""
            提取的信息摘要:
            
            姓名: {data['name'] or '未提供'}
            年龄: {data['age'] or '未提供'}
            位置: {data['location'] or '未提供'}
            职业: {data['occupation'] or '未提供'}
            兴趣爱好: {', '.join(data['interests']) if data['interests'] else '未提供'}
            """
            
            return {
              "formatted_data": formatted_data.strip(),
              "valid": True,
              "raw_data": data
            }
          except Exception as e:
            return {
              "formatted_data": f"处理数据时出错: {str(e)}",
              "valid": False
            }
      variables: []
      outputs:
        formatted_data:
          type: string
        valid:
          type: boolean
        raw_data:
          type: object
      availablePrevNodes: []
      availableNextNodes: []
    
    generate_response:
      type: llm
      title: "生成回复"
      model:
        provider: langgenius/openai_api_compatible/openai_api_compatible
        name: glm-4-flashx
        mode: chat
      prompt_template:
        - id: system-prompt
          role: system
          text: '{{#context#}}
            你是一个专业的助手。根据提取的数据，生成一个友好的回复给用户，确认已提取的信息并询问是否有需要修正的地方。'
        - id: human-prompt
          role: human
          text: '请根据以下提取的数据生成回复：{{formatted_data}}'
      variables: []
      outputs:
        text:
          type: string
      context:
        enabled: true
        variable_selector:
          - process_data
          - formatted_data
      availablePrevNodes: []
      availableNextNodes: []
  
  edges:
    - source: start_node
      target: extract_data
      sourceHandle: ""
      targetHandle: ""
      sourceType: start
      targetType: llm
    
    - source: extract_data
      target: process_data
      sourceHandle: ""
      targetHandle: ""
      sourceType: llm
      targetType: code
    
    - source: process_data
      target: generate_response
      sourceHandle: ""
      targetHandle: ""
      sourceType: code
      targetType: llm
```
