app:
  description: 多查询转换：将用户问题拆解为多个子问题，单独进行召回，再作为上下文回答原始问题
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: 【测试】MultiQuery-RAG
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/siliconflow:0.0.8@217f973bd7ced1b099c2f0c669f1356bdf4cc38b8372fd58d7874f9940b95de3
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1744182759966-source-1744182916650-target
      selected: false
      source: '1744182759966'
      sourceHandle: source
      target: '1744182916650'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: code
      id: 1744182916650-source-1744187264400-target
      selected: false
      source: '1744182916650'
      sourceHandle: source
      target: '1744187264400'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: code
        targetType: iteration
      id: 1744187264400-source-1744193760898-target
      selected: false
      source: '1744187264400'
      sourceHandle: source
      target: '1744193760898'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: true
        isInLoop: false
        iteration_id: '1744193760898'
        sourceType: iteration-start
        targetType: knowledge-retrieval
      id: 1744193760898start-source-1744193769083-target
      selected: false
      source: 1744193760898start
      sourceHandle: source
      target: '1744193769083'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: iteration
        targetType: llm
      id: 1744193760898-source-1744194276307-target
      selected: false
      source: '1744193760898'
      sourceHandle: source
      target: '1744194276307'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: end
      id: 1744194276307-source-1744248350504-target
      selected: false
      source: '1744194276307'
      sourceHandle: source
      target: '1744248350504'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: query
          max_length: 48
          options: []
          required: true
          type: text-input
          variable: query
        - label: child_query_num
          max_length: 3
          options: []
          required: true
          type: text-input
          variable: child_query_num
      height: 114
      id: '1744182759966'
      position:
        x: 30
        y: 257
      positionAbsolute:
        x: 30
        y: 257
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-7B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 0adc35cb-5690-4bc5-abca-dd04ea4cda41
          role: system
          text: 给定以下问题，生成{{#1744182759966.child_query_num#}}个语义相似的具体的子问题，用中文输出，每个问题换行，不要有多余的回答。原始问题：{{#1744182759966.query#}}
        selected: false
        title: LLM
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744182916650'
      position:
        x: 332
        y: 257
      positionAbsolute:
        x: 332
        y: 257
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        code: "def main(arg: str) -> dict:\n    return {\n        \"result\": [x for\
          \ x in arg.strip().split(\"\\n\") if x]\n    }\n   "
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: array[string]
        selected: false
        title: 代码执行
        type: code
        variables:
        - value_selector:
          - '1744182916650'
          - text
          variable: arg
      height: 52
      id: '1744187264400'
      position:
        x: 634
        y: 257
      positionAbsolute:
        x: 634
        y: 257
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        desc: ''
        error_handle_mode: terminated
        height: 178
        is_parallel: false
        iterator_selector:
        - '1744187264400'
        - result
        output_selector:
        - '1744193769083'
        - result
        output_type: array[object]
        parallel_nums: 10
        selected: false
        start_node_id: 1744193760898start
        title: 迭代
        type: iteration
        width: 386
      height: 178
      id: '1744193760898'
      position:
        x: 936
        y: 257
      positionAbsolute:
        x: 936
        y: 257
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 386
      zIndex: 1
    - data:
        desc: ''
        isInIteration: true
        selected: false
        title: ''
        type: iteration-start
      draggable: false
      height: 48
      id: 1744193760898start
      parentId: '1744193760898'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 960
        y: 325
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-iteration-start
      width: 44
      zIndex: 1002
    - data:
        dataset_ids:
        - 2250ed95-a817-436a-b1d0-5c04baea5cc3
        desc: ''
        isInIteration: true
        isInLoop: false
        iteration_id: '1744193760898'
        multiple_retrieval_config:
          reranking_enable: false
          reranking_mode: reranking_model
          reranking_model:
            model: netease-youdao/bce-reranker-base_v1
            provider: langgenius/siliconflow/siliconflow
          top_k: 4
        query_variable_selector:
        - '1744193760898'
        - item
        retrieval_mode: multiple
        selected: false
        title: 知识检索
        type: knowledge-retrieval
      height: 90
      id: '1744193769083'
      parentId: '1744193760898'
      position:
        x: 128
        y: 68
      positionAbsolute:
        x: 1064
        y: 325
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        context:
          enabled: true
          variable_selector:
          - '1744193760898'
          - output
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-72B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 00e88db7-e387-4d6f-bf65-b86af5089e0a
          role: system
          text: '请根据以下检索到的文档内容和问题生成一个详细且准确的回答，如果文档内容与问题无关，请回答不知道。

            文档内容：{{#context#}}

            问题：{{#1744182759966.query#}}

            回答：'
        selected: false
        title: LLM 2
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744194276307'
      position:
        x: 1382
        y: 257
      positionAbsolute:
        x: 1382
        y: 257
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1744194276307'
          - text
          variable: answer
        selected: false
        title: 结束
        type: end
      height: 88
      id: '1744248350504'
      position:
        x: 1684
        y: 257
      positionAbsolute:
        x: 1684
        y: 257
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        author: tanchg
        desc: ''
        height: 99
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"多查询转换：将用户问题拆解为多个子问题，单独进行检索召回，再作为上下文回答用户问题","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":"font-size:
          16px;"}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: blue
        title: ''
        type: ''
        width: 692
      height: 99
      id: '1744698190787'
      position:
        x: 74.87826100043708
        y: 425.6731576373408
      positionAbsolute:
        x: 74.87826100043708
        y: 425.6731576373408
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 692
    viewport:
      x: 61.457122734069344
      y: 147.03340682032865
      zoom: 0.8621098152866349
