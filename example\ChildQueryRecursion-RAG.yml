app:
  description: 子问题递归查询转换：将用户问题拆解为多个子问题，单独召回以及回答，将上一个子问题的回答作为下一个问题的上下文，最后回答用户的原始问题。
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: 【测试】ChildQueryRecursion-RAG
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/siliconflow:0.0.8@217f973bd7ced1b099c2f0c669f1356bdf4cc38b8372fd58d7874f9940b95de3
kind: app
version: 0.1.5
workflow:
  conversation_variables:
  - description: 子问题对应的回答
    id: ab111a1f-abf2-4156-a940-c8c327c6c8fb
    name: queryAnwser
    selector:
    - conversation
    - queryAnwser
    value: ''
    value_type: string
  - description: 子问题数组
    id: a7422981-d179-435a-bada-55c0c9cd5093
    name: quertList
    selector:
    - conversation
    - quertList
    value: []
    value_type: array[string]
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        sourceType: start
        targetType: llm
      id: 1744593277938-llm
      selected: false
      source: '1744593277938'
      sourceHandle: source
      target: llm
      targetHandle: target
      type: custom
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: code
      id: llm-source-1744593566307-target
      selected: false
      source: llm
      sourceHandle: source
      target: '1744593566307'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: code
        targetType: loop
      id: 1744593566307-source-1744593592934-target
      selected: false
      source: '1744593566307'
      sourceHandle: source
      target: '1744593592934'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744593592934'
        sourceType: loop-start
        targetType: code
      id: 1744593592934start-source-1744593617786-target
      selected: false
      source: 1744593592934start
      sourceHandle: source
      target: '1744593617786'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744593592934'
        sourceType: knowledge-retrieval
        targetType: llm
      id: 1744593758714-source-1744593775987-target
      selected: false
      source: '1744593758714'
      sourceHandle: source
      target: '1744593775987'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744593592934'
        sourceType: llm
        targetType: assigner
      id: 1744593775987-source-1744594228197-target
      selected: false
      source: '1744593775987'
      sourceHandle: source
      target: '1744594228197'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: assigner
      id: 1744595369084-source-1744597368675-target
      source: '1744595369084'
      sourceHandle: source
      target: '1744597368675'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: assigner
        targetType: answer
      id: 1744597368675-source-answer-target
      source: '1744597368675'
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: loop
        targetType: llm
      id: 1744593592934-source-1744595369084-target
      source: '1744593592934'
      sourceHandle: source
      target: '1744595369084'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744593592934'
        sourceType: code
        targetType: knowledge-retrieval
      id: 1744593617786-source-1744593758714-target
      source: '1744593617786'
      sourceHandle: source
      target: '1744593758714'
      targetHandle: target
      type: custom
      zIndex: 1002
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: number
          max_length: 48
          options: []
          required: true
          type: number
          variable: number
      height: 88
      id: '1744593277938'
      position:
        x: 30
        y: 343.5
      positionAbsolute:
        x: 30
        y: 343.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        memory:
          query_prompt_template: '{{#sys.query#}}'
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 10
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-7B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 96781356-9494-4eca-bd46-fd895a136a10
          role: system
          text: '给定以下问题，生成{{#1744593277938.number#}}个语义相似的具体的子问题，用中文输出，每个问题换行，不要有多余的回答。

            原始问题：{{#sys.query#}}'
        selected: false
        title: LLM 子问题生成
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: llm
      position:
        x: 332
        y: 343.5
      positionAbsolute:
        x: 332
        y: 343.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        answer: '{{#1744595369084.text#}}'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 103
      id: answer
      position:
        x: 2979
        y: 343.5
      positionAbsolute:
        x: 2979
        y: 343.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        code: "def main(arg: str) -> dict:\n    processed = [x for x in arg.strip().split(\"\
          \\n\") if x]\n    processed.append(\"结束\")\n    return {\"result\": processed}\n"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: array[string]
        selected: false
        title: 代码 子问题-->数组
        type: code
        variables:
        - value_selector:
          - llm
          - text
          variable: arg
      height: 52
      id: '1744593566307'
      position:
        x: 634
        y: 343.5
      positionAbsolute:
        x: 634
        y: 343.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        break_conditions:
        - comparison_operator: empty
          id: afe7318376a24ea09797001f8937fda3
          value: ''
          varType: array[string]
          variable_selector:
          - '1744593617786'
          - result
        desc: ''
        error_handle_mode: terminated
        height: 287
        logical_operator: and
        loop_count: 4
        selected: false
        start_node_id: 1744593592934start
        title: 子问题回答递归回答
        type: loop
        width: 1379
      height: 287
      id: '1744593592934'
      position:
        x: 936
        y: 343.5
      positionAbsolute:
        x: 936
        y: 343.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 1379
      zIndex: 1
    - data:
        desc: ''
        isInLoop: true
        selected: false
        title: ''
        type: loop-start
      draggable: false
      height: 48
      id: 1744593592934start
      parentId: '1744593592934'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 960
        y: 411.5
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-loop-start
      width: 44
      zIndex: 1002
    - data:
        code: "def main(arg: list, arg2: list) -> dict:\n    queryList = []\n    if\
          \ len(arg2) > 0:\n        queryList = arg2\n    else:\n        queryList\
          \ = arg\n    \n    if not queryList: \n        return {\"result\": [], \"\
          query\": None}\n    \n    query = queryList.pop(0)\n    return {\n     \
          \   \"result\": queryList,\n        \"query\": query\n    }\n"
        code_language: python3
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744593592934'
        outputs:
          query:
            children: null
            type: string
          result:
            children: null
            type: array[string]
        selected: false
        title: 代码 子问题数组初始化
        type: code
        variables:
        - value_selector:
          - '1744593566307'
          - result
          variable: arg
        - value_selector:
          - conversation
          - quertList
          variable: arg2
      height: 52
      id: '1744593617786'
      parentId: '1744593592934'
      position:
        x: 128
        y: 66.57142857142856
      positionAbsolute:
        x: 1064
        y: 410.07142857142856
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        dataset_ids:
        - 2250ed95-a817-436a-b1d0-5c04baea5cc3
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744593592934'
        multiple_retrieval_config:
          reranking_enable: false
          reranking_mode: reranking_model
          reranking_model:
            model: netease-youdao/bce-reranker-base_v1
            provider: langgenius/siliconflow/siliconflow
          top_k: 4
        query_variable_selector:
        - '1744593617786'
        - query
        retrieval_mode: multiple
        selected: false
        title: 知识检索
        type: knowledge-retrieval
      height: 90
      id: '1744593758714'
      parentId: '1744593592934'
      position:
        x: 458.57142857142867
        y: 65
      positionAbsolute:
        x: 1394.5714285714287
        y: 408.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        context:
          enabled: true
          variable_selector:
          - '1744593758714'
          - result
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744593592934'
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-7B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 71d49d8e-c780-4559-8f00-cf4a575ea43d
          role: system
          text: '请根据以下检索到的文档内容和用户提出的问题生成一个详细且准确的回答，如果文档内容与问题无关，请回答不知道。

            文档内容：{{#context#}}\n{{#conversation.queryAnwser#}}

            问题：{{#1744593617786.query#}}

            回答：'
        selected: false
        title: LLM 子问题回答
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744593775987'
      parentId: '1744593592934'
      position:
        x: 767.7142857142858
        y: 65
      positionAbsolute:
        x: 1703.7142857142858
        y: 408.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1744593775987'
          - text
          variable_selector:
          - conversation
          - queryAnwser
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1744593617786'
          - result
          variable_selector:
          - conversation
          - quertList
          write_mode: over-write
        loop_id: '1744593592934'
        selected: false
        title: 变量赋值 2
        type: assigner
        version: '2'
      height: 114
      id: '1744594228197'
      parentId: '1744593592934'
      position:
        x: 1069.7142857142858
        y: 65
      positionAbsolute:
        x: 2005.7142857142858
        y: 408.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        context:
          enabled: true
          variable_selector:
          - conversation
          - queryAnwser
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-72B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 292f5968-ff62-4e66-915b-85b9fb8ecfb1
          role: system
          text: '请根据以下检索到的文档内容和问题生成一个详细且准确的回答，如果文档内容与问题无关，请回答不知道。

            文档内容：{{#context#}}

            问题：{{#sys.query#}}

            回答：'
        selected: false
        title: LLM 3
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744595369084'
      position:
        x: 2376.4285714285716
        y: 343.5
      positionAbsolute:
        x: 2376.4285714285716
        y: 343.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: clear
          value: ''
          variable_selector:
          - conversation
          - queryAnwser
          write_mode: over-write
        - input_type: variable
          operation: clear
          value: ''
          variable_selector:
          - conversation
          - quertList
          write_mode: over-write
        selected: false
        title: 变量赋值 4
        type: assigner
        version: '2'
      height: 114
      id: '1744597368675'
      position:
        x: 2677
        y: 343.5
      positionAbsolute:
        x: 2677
        y: 343.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        author: tanchg
        desc: ''
        height: 154
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"子问题递归查询转换：将用户问题拆解为多个子问题，单独召回以及回答，同时将上一个子问题的回答作为下一个子问题的上下文，最后回答用户的原始问题。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":"font-size:
          16px;"}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: blue
        title: ''
        type: ''
        width: 568
      height: 154
      id: '1744698392205'
      position:
        x: 61.76614886754521
        y: 526.0760976753197
      positionAbsolute:
        x: 61.76614886754521
        y: 526.0760976753197
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 568
    viewport:
      x: 312.98185152764364
      y: 179.60470590329822
      zoom: 0.5144070680984107
