# Dify 写作助手工作流项目

## 项目概述

本项目包含了一个完整的Dify写作助手工作流的分析和实现，支持多文件上传、OCR识别、知识库检索、模板化文档生成等功能。

## 文件说明

### 分析文档
- `写作助手DSL分析报告.md` - 完整的DSL分析报告，包含：
  - 需求分析
  - 节点分析
  - 数据流转说明
  - 变量使用详细说明
  - 模板使用指南
  - 接口调用说明
  - 完整的代码结构说明

### DSL文件
- `【生产2】- 写作助手 shangzy (8).yml` - 原始DSL文件（987行）
- `写作助手-完整可导入版本.yml` - 可导入的DSL文件副本
- `回复助手 shangzy (7).yml` - 回复助手版本
- `简化版写作助手.yml` - 简化版本
- `新版写作助手.yml` - 新版本

### 其他文件
- `dify-workflow-troubleshooting.md` - 工作流故障排除指南
- `dify-file-upload-test.http` - 文件上传测试
- `dify-file-input-example.py` - 文件输入示例

## 快速开始

### 1. 导入工作流
1. 打开Dify工作流编辑器
2. 选择"导入"功能
3. 上传 `写作助手-完整可导入版本.yml` 文件
4. 确认导入设置

### 2. 配置依赖
确保以下插件已安装：
- `fetus/template-tools:0.0.7`
- `langgenius/openai_api_compatible:0.1.11`
- `fetus/ocr-tools:0.0.2`

### 3. 配置参数
根据实际需求配置以下参数：
- 知识库API Token
- 租户ID (tenantid)
- 知识库ID列表 (personalLibs)

## 功能特性

### 多模态输入支持
- **文档处理**：支持DOC、DOCX、PDF、TXT等格式
- **图片识别**：支持PNG、JPG、JPEG、BMP格式的OCR识别
- **知识库检索**：支持多知识库并行检索
- **模板处理**：支持复杂的模板语法和变量替换

### 文件上传限制
- 图片文件：最大10MB
- 文档文件：最大15MB
- 音频文件：最大50MB
- 视频文件：最大100MB
- 批量上传：最多5个文件

### 变量系统
系统定义了4个核心变量：
- `DOCS_TEXT_ARRAY`：文档解析结果数组
- `OCR_TEXT`：OCR识别文本
- `KOWNLAGE_TEXT`：知识库检索结果
- `TEMPLATE_TEXT`：模板解析结果

## 工作流程

1. **输入处理**：接收用户输入的文件、参数和配置
2. **条件判断**：根据输入类型分发到不同的处理流程
3. **并行处理**：同时处理文档、图片、知识库和模板
4. **数据聚合**：将所有处理结果汇总
5. **智能生成**：根据模板或直接生成内容
6. **结果输出**：提供文件下载或文本显示

## 模板语法

支持以下模板语法：
```handlebars
# 变量替换
{{变量名}}

# 条件判断
{{#if 条件}}
内容
{{/if}}

# 循环遍历
{{#each 数组}}
- {{this}}
{{/each}}
```

## API接口

### 知识库检索
```bash
POST /api/knowledge/search
Headers:
  Token: your_token
  Content-Type: application/json
Body:
{
  "tenantid": "tenant_123",
  "personalLibs": "lib1,lib2",
  "query": "搜索关键词",
  "limit": 10
}
```

### OCR识别
```bash
POST /api/ocr/recognize
Headers:
  Content-Type: multipart/form-data
Body:
  file: <图片文件>
  language: "zh_CN"
```

## 故障排除

如遇到问题，请参考：
1. `dify-workflow-troubleshooting.md` - 详细的故障排除指南
2. `写作助手DSL分析报告.md` - 完整的技术文档

## 版本历史

- **v8** (当前版本)：添加了输入增强、输出增强、多文件上传功能
- **v7**：回复助手版本
- **v6**：基础版本

## 贡献

欢迎提交Issue和Pull Request来改进这个工作流。

## 许可证

本项目遵循相应的开源许可证。 