import os
import uuid
import subprocess
from typing import List, Optional
from fastapi import FastAPI, UploadFile, File, Form, HTTPException
from fastapi.responses import FileResponse, PlainTextResponse
import shutil
import tempfile

app = FastAPI(title="Pandoc API", description="API for converting documents using Pandoc with Chinese support")

TEMP_DIR = "/app/temp"
os.makedirs(TEMP_DIR, exist_ok=True)

@app.get("/health")
def health_check():
    """健康检查端点"""
    return {"status": "healthy", "service": "pandoc-api"}

@app.post("/convert/text", response_class=PlainTextResponse)
async def convert_text(
    content: str = Form(...),
    from_format: str = Form(...),
    to_format: str = Form(...),
    options: Optional[str] = Form(None)
):
    """
    将文本从一种格式转换为另一种格式
    
    - **content**: 要转换的文本内容
    - **from_format**: 输入格式 (如 markdown, html)
    - **to_format**: 输出格式 (如 html, docx)
    - **options**: 可选的pandoc参数，用空格分隔
    """
    # 创建唯一的临时文件名
    input_file = os.path.join(TEMP_DIR, f"{uuid.uuid4()}.{from_format}")
    output_file = os.path.join(TEMP_DIR, f"{uuid.uuid4()}.{to_format}")
    
    try:
        # 写入输入文件
        with open(input_file, "w", encoding="utf-8") as f:
            f.write(content)
        
        # 构建pandoc命令
        cmd = ["pandoc", "-f", from_format, "-t", to_format, "-o", output_file, input_file]
        
        # 添加额外选项
        if options:
            cmd.extend(options.split())
        
        # 执行pandoc命令
        process = subprocess.run(cmd, capture_output=True, text=True)
        
        if process.returncode != 0:
            raise HTTPException(status_code=500, detail=f"Pandoc转换失败: {process.stderr}")
        
        # 如果输出格式是文本格式，直接返回内容
        if to_format in ["html", "markdown", "md", "plain", "txt"]:
            with open(output_file, "r", encoding="utf-8") as f:
                return f.read()
        else:
            # 否则返回文件
            return FileResponse(
                output_file,
                media_type="application/octet-stream",
                filename=f"output.{to_format}"
            )
    
    finally:
        # 清理临时文件
        for file in [input_file, output_file]:
            if os.path.exists(file):
                try:
                    os.remove(file)
                except:
                    pass

@app.post("/convert/file")
async def convert_file(
    file: UploadFile = File(...),
    from_format: Optional[str] = Form(None),
    to_format: str = Form(...),
    options: Optional[str] = Form(None)
):
    """
    将上传的文件从一种格式转换为另一种格式
    
    - **file**: 要转换的文件
    - **from_format**: 输入格式 (如果为空，将从文件扩展名推断)
    - **to_format**: 输出格式
    - **options**: 可选的pandoc参数，用空格分隔
    """
    # 创建唯一的临时文件名
    input_filename = file.filename or f"upload_{uuid.uuid4()}"
    input_file = os.path.join(TEMP_DIR, input_filename)
    output_file = os.path.join(TEMP_DIR, f"{uuid.uuid4()}.{to_format}")
    
    try:
        # 保存上传的文件
        with open(input_file, "wb") as f:
            content = await file.read()
            f.write(content)
        
        # 如果未指定输入格式，从文件名推断
        if not from_format:
            ext = os.path.splitext(input_filename)[1].lstrip('.')
            if ext:
                from_format = ext
            else:
                from_format = "markdown"  # 默认格式
        
        # 构建pandoc命令
        cmd = ["pandoc", "-f", from_format, "-t", to_format, "-o", output_file, input_file]
        
        # 添加额外选项
        if options:
            cmd.extend(options.split())
        
        # 执行pandoc命令
        process = subprocess.run(cmd, capture_output=True, text=True)
        
        if process.returncode != 0:
            raise HTTPException(status_code=500, detail=f"Pandoc转换失败: {process.stderr}")
        
        # 如果输出格式是文本格式，直接返回内容
        if to_format in ["html", "markdown", "md", "plain", "txt"]:
            with open(output_file, "r", encoding="utf-8") as f:
                return PlainTextResponse(f.read())
        else:
            # 否则返回文件
            return FileResponse(
                output_file,
                media_type="application/octet-stream",
                filename=f"output.{to_format}"
            )
    
    finally:
        # 清理临时文件
        for file in [input_file, output_file]:
            if os.path.exists(file):
                try:
                    os.remove(file)
                except:
                    pass

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)