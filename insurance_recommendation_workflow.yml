kind: app
version: 0.1.5
app:
  mode: workflow
  name: 个性化保险计划推荐系统
  description: "根据客户画像，分析客户需求与生活方式，提供定制的保险解决方案"
  icon: "💼"
  icon_background: "#FFEAD5"
  use_icon_as_answer_icon: false

workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
    opening_statement: ''
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
      - source: start_node
        target: classify_needs
        id: start_node-source-classify_needs-target
        data:
          sourceType: start
          targetType: question-classifier
          isInIteration: false
        sourceHandle: source
        targetHandle: target
        type: custom
        zIndex: 0
      - source: classify_needs
        target: analyze_customer
        id: classify_needs-source-analyze_customer-target
        data:
          sourceType: question-classifier
          targetType: llm
          isInIteration: false
        sourceHandle: source
        targetHandle: target
        type: custom
        zIndex: 0
      - source: analyze_customer
        target: customer_type_branch
        id: analyze_customer-source-customer_type_branch-target
        data:
          sourceType: llm
          targetType: if-else
          isInIteration: false
        sourceHandle: source
        targetHandle: target
        type: custom
        zIndex: 0
      - source: customer_type_branch
        target: match_products_young
        id: customer_type_branch-source-match_products_young-target
        data:
          sourceType: if-else
          targetType: llm
          isInIteration: false
        sourceHandle: '1'
        targetHandle: target
        type: custom
        zIndex: 0
      - source: customer_type_branch
        target: match_products_family
        id: customer_type_branch-source-match_products_family-target
        data:
          sourceType: if-else
          targetType: llm
          isInIteration: false
        sourceHandle: '2'
        targetHandle: target
        type: custom
        zIndex: 0
      - source: customer_type_branch
        target: match_products_parent
        id: customer_type_branch-source-match_products_parent-target
        data:
          sourceType: if-else
          targetType: llm
          isInIteration: false
        sourceHandle: '3'
        targetHandle: target
        type: custom
        zIndex: 0
      - source: customer_type_branch
        target: match_products_middle
        id: customer_type_branch-source-match_products_middle-target
        data:
          sourceType: if-else
          targetType: llm
          isInIteration: false
        sourceHandle: '4'
        targetHandle: target
        type: custom
        zIndex: 0
      - source: customer_type_branch
        target: match_products_retire
        id: customer_type_branch-source-match_products_retire-target
        data:
          sourceType: if-else
          targetType: llm
          isInIteration: false
        sourceHandle: '5'
        targetHandle: target
        type: custom
        zIndex: 0
      - source: match_products_young
        target: calculate_premium
        id: match_products_young-source-calculate_premium-target
        data:
          sourceType: llm
          targetType: code
          isInIteration: false
        sourceHandle: source
        targetHandle: target
        type: custom
        zIndex: 0
      - source: match_products_family
        target: calculate_premium
        id: match_products_family-source-calculate_premium-target
        data:
          sourceType: llm
          targetType: code
          isInIteration: false
        sourceHandle: source
        targetHandle: target
        type: custom
        zIndex: 0
      - source: match_products_parent
        target: calculate_premium
        id: match_products_parent-source-calculate_premium-target
        data:
          sourceType: llm
          targetType: code
          isInIteration: false
        sourceHandle: source
        targetHandle: target
        type: custom
        zIndex: 0
      - source: match_products_middle
        target: calculate_premium
        id: match_products_middle-source-calculate_premium-target
        data:
          sourceType: llm
          targetType: code
          isInIteration: false
        sourceHandle: source
        targetHandle: target
        type: custom
        zIndex: 0
      - source: match_products_retire
        target: calculate_premium
        id: match_products_retire-source-calculate_premium-target
        data:
          sourceType: llm
          targetType: code
          isInIteration: false
        sourceHandle: source
        targetHandle: target
        type: custom
        zIndex: 0
      - source: calculate_premium
        target: generate_report
        id: calculate_premium-source-generate_report-target
        data:
          sourceType: code
          targetType: llm
          isInIteration: false
        sourceHandle: source
        targetHandle: target
        type: custom
        zIndex: 0
      - source: generate_report
        target: end_node
        id: generate_report-source-end_node-target
        data:
          sourceType: llm
          targetType: end
          isInIteration: false
        sourceHandle: source
        targetHandle: target
        type: custom
        zIndex: 0

    nodes:
      - data:
          type: start
          title: 收集客户信息
          variables:
            - type: paragraph
              variable: age
              label: 年龄
              required: true
              max_length: 10
            - type: paragraph
              variable: gender
              label: 性别
              required: true
              max_length: 10
            - type: paragraph
              variable: marital_status
              label: 婚姻状况
              required: true
              max_length: 20
            - type: paragraph
              variable: children
              label: 子女情况
              required: true
              max_length: 100
            - type: paragraph
              variable: occupation
              label: 职业
              required: true
              max_length: 50
            - type: paragraph
              variable: income
              label: 月收入(元)
              required: true
              max_length: 20
            - type: paragraph
              variable: existing_insurance
              label: 现有保险
              required: false
              max_length: 200
            - type: paragraph
              variable: health_status
              label: 健康状况
              required: true
              max_length: 200
            - type: paragraph
              variable: lifestyle
              label: 生活方式
              required: true
              max_length: 200
            - type: paragraph
              variable: risk_preference
              label: 风险偏好
              required: true
              max_length: 50
            - type: paragraph
              variable: special_needs
              label: 特殊需求
              required: false
              max_length: 200
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: start_node

      - data:
          type: question-classifier
          title: 确定客户主要需求类型
          classes:
            - id: '1'
              name: 健康保障需求
            - id: '2'
              name: 财产保障需求
            - id: '3'
              name: 家庭保障需求
            - id: '4'
              name: 退休规划需求
            - id: '5'
              name: 子女教育需求
          model:
            provider: langgenius/openai_api_compatible/openai_api_compatible
            name: glm-4-flashx
            mode: chat
            completion_params:
              temperature: 0.3
          query_variable_selector:
            - start_node
            - special_needs
          outputs:
            class_name:
              type: string
            class_id:
              type: string
          context:
            enabled: true
            variable_selector:
              - start_node
              - age
              - gender
              - marital_status
              - children
              - occupation
              - income
              - existing_insurance
              - health_status
              - lifestyle
              - risk_preference
              - special_needs
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: classify_needs

      - data:
          type: llm
          title: 客户画像分析
          model:
            provider: langgenius/openai_api_compatible/openai_api_compatible
            name: glm-4-flashx
            mode: chat
            completion_params:
              temperature: 0.5
          prompt_template:
            - id: system-prompt
              role: system
              text: '{{#context#}}

              你是一位专业的保险顾问，请根据客户提供的信息，分析客户的生命周期阶段、风险承受能力、保障缺口和优先保障需求。请提供详细的客户画像分析。'
            - id: user-prompt
              role: user
              text: |
                请根据以下客户信息进行分析：

                年龄：{{#start_node.age#}}
                性别：{{#start_node.gender#}}
                婚姻状况：{{#start_node.marital_status#}}
                子女情况：{{#start_node.children#}}
                职业：{{#start_node.occupation#}}
                月收入：{{#start_node.income#}}元
                现有保险：{{#start_node.existing_insurance#}}
                健康状况：{{#start_node.health_status#}}
                生活方式：{{#start_node.lifestyle#}}
                风险偏好：{{#start_node.risk_preference#}}
                特殊需求：{{#start_node.special_needs#}}

                主要需求类型：{{#classify_needs.class_name#}}

                请提供以下分析：
                1. 客户生命周期阶段
                2. 风险承受能力评估
                3. 保障缺口分析
                4. 优先保障需求
                5. 客户类型（年轻单身型/新婚家庭型/育儿家庭型/中年稳定型/退休准备型）
          context:
            enabled: true
            variable_selector:
              - start_node
              - age
              - gender
              - marital_status
              - children
              - occupation
              - income
              - existing_insurance
              - health_status
              - lifestyle
              - risk_preference
              - special_needs
              - classify_needs
              - class_name
          outputs:
            text:
              type: string
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: analyze_customer

      - data:
          type: if-else
          title: 客户类型分流
          cases:
            - case_id: '1'
              conditions:
                - comparison_operator: contains
                  id: condition1
                  value: 年轻单身型
                  varType: string
                  variable_selector:
                    - analyze_customer
                    - text
              id: '1'
              logical_operator: and
            - case_id: '2'
              conditions:
                - comparison_operator: contains
                  id: condition2
                  value: 新婚家庭型
                  varType: string
                  variable_selector:
                    - analyze_customer
                    - text
              id: '2'
              logical_operator: and
            - case_id: '3'
              conditions:
                - comparison_operator: contains
                  id: condition3
                  value: 育儿家庭型
                  varType: string
                  variable_selector:
                    - analyze_customer
                    - text
              id: '3'
              logical_operator: and
            - case_id: '4'
              conditions:
                - comparison_operator: contains
                  id: condition4
                  value: 中年稳定型
                  varType: string
                  variable_selector:
                    - analyze_customer
                    - text
              id: '4'
              logical_operator: and
            - case_id: '5'
              conditions:
                - comparison_operator: contains
                  id: condition5
                  value: 退休准备型
                  varType: string
                  variable_selector:
                    - analyze_customer
                    - text
              id: '5'
              logical_operator: and
          outputs:
            case_id:
              type: string
          context:
            enabled: true
            variable_selector:
              - analyze_customer
              - text
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: customer_type_branch

      - data:
          type: llm
          title: 年轻单身型产品匹配
          model:
            provider: langgenius/openai_api_compatible/openai_api_compatible
            name: glm-4-flashx
            mode: chat
            completion_params:
              temperature: 0.5
          prompt_template:
            - id: system-prompt
              role: system
              text: '{{#context#}}

              你是一位专业的保险顾问，请为年轻单身客户推荐合适的保险产品组合。'
            - id: user-prompt
              role: user
              text: |
                客户信息：

                年龄：{{#start_node.age#}}
                性别：{{#start_node.gender#}}
                职业：{{#start_node.occupation#}}
                月收入：{{#start_node.income#}}元
                健康状况：{{#start_node.health_status#}}
                风险偏好：{{#start_node.risk_preference#}}

                客户画像分析：
                {{#analyze_customer.text#}}

                请为该年轻单身客户推荐合适的保险产品组合，包括：
                1. 医疗保险
                2. 重疾险
                3. 意外险
                4. 定期寿险
                5. 其他适合的保险产品

                对于每个推荐的产品，请提供：
                - 产品类型
                - 建议保额
                - 保障期限
                - 产品特点
                - 推荐理由

                请以JSON格式输出，格式如下：
                ```json
                {
                  "products": [
                    {
                      "type": "产品类型",
                      "coverage": "建议保额",
                      "term": "保障期限",
                      "features": "产品特点",
                      "reason": "推荐理由"
                    }
                  ]
                }
                ```
          context:
            enabled: true
            variable_selector:
              - start_node
              - age
              - gender
              - occupation
              - income
              - health_status
              - risk_preference
              - analyze_customer
              - text
          outputs:
            text:
              type: string
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: match_products_young

      - data:
          type: llm
          title: 新婚家庭型产品匹配
          model:
            provider: langgenius/openai_api_compatible/openai_api_compatible
            name: glm-4-flashx
            mode: chat
            completion_params:
              temperature: 0.5
          prompt_template:
            - id: system-prompt
              role: system
              text: '{{#context#}}

              你是一位专业的保险顾问，请为新婚家庭客户推荐合适的保险产品组合。'
            - id: user-prompt
              role: user
              text: |
                客户信息：

                年龄：{{#start_node.age#}}
                性别：{{#start_node.gender#}}
                婚姻状况：{{#start_node.marital_status#}}
                职业：{{#start_node.occupation#}}
                月收入：{{#start_node.income#}}元
                健康状况：{{#start_node.health_status#}}
                风险偏好：{{#start_node.risk_preference#}}

                客户画像分析：
                {{#analyze_customer.text#}}

                请为该新婚家庭客户推荐合适的保险产品组合，包括：
                1. 医疗保险
                2. 重疾险
                3. 意外险
                4. 定期寿险
                5. 家财险
                6. 其他适合的保险产品

                对于每个推荐的产品，请提供：
                - 产品类型
                - 建议保额
                - 保障期限
                - 产品特点
                - 推荐理由

                请以JSON格式输出，格式如下：
                ```json
                {
                  "products": [
                    {
                      "type": "产品类型",
                      "coverage": "建议保额",
                      "term": "保障期限",
                      "features": "产品特点",
                      "reason": "推荐理由"
                    }
                  ]
                }
                ```
          context:
            enabled: true
            variable_selector:
              - start_node
              - age
              - gender
              - marital_status
              - occupation
              - income
              - health_status
              - risk_preference
              - analyze_customer
              - text
          outputs:
            text:
              type: string
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: match_products_family

      - data:
          type: llm
          title: 育儿家庭型产品匹配
          model:
            provider: langgenius/openai_api_compatible/openai_api_compatible
            name: glm-4-flashx
            mode: chat
            completion_params:
              temperature: 0.5
          prompt_template:
            - id: system-prompt
              role: system
              text: '{{#context#}}

              你是一位专业的保险顾问，请为育儿家庭客户推荐合适的保险产品组合。'
            - id: user-prompt
              role: user
              text: |
                客户信息：

                年龄：{{#start_node.age#}}
                性别：{{#start_node.gender#}}
                婚姻状况：{{#start_node.marital_status#}}
                子女情况：{{#start_node.children#}}
                职业：{{#start_node.occupation#}}
                月收入：{{#start_node.income#}}元
                健康状况：{{#start_node.health_status#}}
                风险偏好：{{#start_node.risk_preference#}}

                客户画像分析：
                {{#analyze_customer.text#}}

                请为该育儿家庭客户推荐合适的保险产品组合，包括：
                1. 医疗保险
                2. 重疾险
                3. 意外险
                4. 定期寿险
                5. 家财险
                6. 教育金保险
                7. 其他适合的保险产品

                对于每个推荐的产品，请提供：
                - 产品类型
                - 建议保额
                - 保障期限
                - 产品特点
                - 推荐理由

                请以JSON格式输出，格式如下：
                ```json
                {
                  "products": [
                    {
                      "type": "产品类型",
                      "coverage": "建议保额",
                      "term": "保障期限",
                      "features": "产品特点",
                      "reason": "推荐理由"
                    }
                  ]
                }
                ```
          context:
            enabled: true
            variable_selector:
              - start_node
              - age
              - gender
              - marital_status
              - children
              - occupation
              - income
              - health_status
              - risk_preference
              - analyze_customer
              - text
          outputs:
            text:
              type: string
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: match_products_parent

      - data:
          type: llm
          title: 中年稳定型产品匹配
          model:
            provider: langgenius/openai_api_compatible/openai_api_compatible
            name: glm-4-flashx
            mode: chat
            completion_params:
              temperature: 0.5
          prompt_template:
            - id: system-prompt
              role: system
              text: '{{#context#}}

              你是一位专业的保险顾问，请为中年稳定型客户推荐合适的保险产品组合。'
            - id: user-prompt
              role: user
              text: |
                客户信息：

                年龄：{{#start_node.age#}}
                性别：{{#start_node.gender#}}
                婚姻状况：{{#start_node.marital_status#}}
                子女情况：{{#start_node.children#}}
                职业：{{#start_node.occupation#}}
                月收入：{{#start_node.income#}}元
                健康状况：{{#start_node.health_status#}}
                风险偏好：{{#start_node.risk_preference#}}

                客户画像分析：
                {{#analyze_customer.text#}}

                请为该中年稳定型客户推荐合适的保险产品组合，包括：
                1. 医疗保险
                2. 重疾险
                3. 意外险
                4. 定期寿险
                5. 家财险
                6. 养老保险
                7. 其他适合的保险产品

                对于每个推荐的产品，请提供：
                - 产品类型
                - 建议保额
                - 保障期限
                - 产品特点
                - 推荐理由

                请以JSON格式输出，格式如下：
                ```json
                {
                  "products": [
                    {
                      "type": "产品类型",
                      "coverage": "建议保额",
                      "term": "保障期限",
                      "features": "产品特点",
                      "reason": "推荐理由"
                    }
                  ]
                }
                ```
          context:
            enabled: true
            variable_selector:
              - start_node
              - age
              - gender
              - marital_status
              - children
              - occupation
              - income
              - health_status
              - risk_preference
              - analyze_customer
              - text
          outputs:
            text:
              type: string
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: match_products_middle

      - data:
          type: llm
          title: 退休准备型产品匹配
          model:
            provider: langgenius/openai_api_compatible/openai_api_compatible
            name: glm-4-flashx
            mode: chat
            completion_params:
              temperature: 0.5
          prompt_template:
            - id: system-prompt
              role: system
              text: '{{#context#}}

              你是一位专业的保险顾问，请为退休准备型客户推荐合适的保险产品组合。'
            - id: user-prompt
              role: user
              text: |
                客户信息：

                年龄：{{#start_node.age#}}
                性别：{{#start_node.gender#}}
                婚姻状况：{{#start_node.marital_status#}}
                子女情况：{{#start_node.children#}}
                职业：{{#start_node.occupation#}}
                月收入：{{#start_node.income#}}元
                健康状况：{{#start_node.health_status#}}
                风险偏好：{{#start_node.risk_preference#}}

                客户画像分析：
                {{#analyze_customer.text#}}

                请为该退休准备型客户推荐合适的保险产品组合，包括：
                1. 医疗保险
                2. 重疾险
                3. 意外险
                4. 养老保险
                5. 长期护理保险
                6. 其他适合的保险产品

                对于每个推荐的产品，请提供：
                - 产品类型
                - 建议保额
                - 保障期限
                - 产品特点
                - 推荐理由

                请以JSON格式输出，格式如下：
                ```json
                {
                  "products": [
                    {
                      "type": "产品类型",
                      "coverage": "建议保额",
                      "term": "保障期限",
                      "features": "产品特点",
                      "reason": "推荐理由"
                    }
                  ]
                }
                ```
          context:
            enabled: true
            variable_selector:
              - start_node
              - age
              - gender
              - marital_status
              - children
              - occupation
              - income
              - health_status
              - risk_preference
              - analyze_customer
              - text
          outputs:
            text:
              type: string
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: match_products_retire

      - data:
          type: code
          title: 保费计算
          code_language: python3
          variables: []
          outputs:
            products:
              type: array[object]
            total_premium:
              type: number
          code: |
            import json
            import re

            def main(**kwargs) -> dict:
                # 检查各个客户类型的输入数据
                text = None

                # 检查中年稳定型数据（优先使用，因为截图显示这是当前测试的路径）
                if 'middle_data' in kwargs and kwargs['middle_data']:
                    text = kwargs['middle_data']

                # 如果没有中年稳定型数据，检查其他类型
                if not text:
                    for data_type in ['young_data', 'family_data', 'parent_data', 'retire_data']:
                        if data_type in kwargs and kwargs[data_type]:
                            text = kwargs[data_type]
                            break

                # 如果仍然没有找到数据，尝试使用上下文变量
                if not text and 'context' in kwargs:
                    context = kwargs['context']
                    for node in ['match_products_middle', 'match_products_young', 'match_products_family', 'match_products_parent', 'match_products_retire']:
                        if hasattr(context, node) and hasattr(getattr(context, node), 'text'):
                            text = getattr(getattr(context, node), 'text')
                            break

                # 如果所有尝试都失败，尝试使用input_data参数（兼容旧版本）
                if not text and 'input_data' in kwargs:
                    input_data = kwargs['input_data']
                    if hasattr(input_data, 'text'):
                        text = input_data.text
                    elif isinstance(input_data, dict) and 'text' in input_data:
                        text = input_data['text']
                    elif isinstance(input_data, str):
                        text = input_data

                # 如果所有尝试都失败，返回错误
                if not text:
                    return {"error": "无法获取输入数据", "products": [], "total_premium": 0}

                # 解析JSON数据
                try:
                    # 尝试直接解析整个文本为JSON
                    products_json = json.loads(text)
                except:
                    # 如果失败，尝试从文本中提取JSON部分
                    json_start = text.find('```json')
                    if json_start == -1:
                        # 尝试其他可能的JSON标记
                        json_start = text.find('{"products":')

                    if json_start != -1:
                        # 如果找到JSON开始标记
                        if '```json' in text:
                            json_end = text.find('```', json_start + 7)
                            if json_end != -1:
                                json_str = text[json_start + 7:json_end].strip()
                        else:
                            # 尝试提取完整的JSON对象
                            json_str = ''
                            brace_count = 0
                            capture = False
                            for i in range(json_start, len(text)):
                                if text[i] == '{':
                                    capture = True
                                    brace_count += 1
                                if capture:
                                    json_str += text[i]
                                if text[i] == '}':
                                    brace_count -= 1
                                    if brace_count == 0 and capture:
                                        break

                        try:
                            products_json = json.loads(json_str)
                        except:
                            # 如果仍然无法解析，返回默认值
                            return {"error": "无法解析JSON数据", "products": [], "total_premium": 0}
                    else:
                        # 如果没有找到JSON标记，返回默认值
                        return {"error": "无法找到JSON数据", "products": [], "total_premium": 0}

                # 提取产品列表
                products = products_json.get("products", [])
                if not products:
                    return {"error": "产品列表为空", "products": [], "total_premium": 0}

                # 模拟保费计算
                for product in products:
                    product_type = product.get("type", "")
                    coverage = product.get("coverage", "")

                    # 提取保额数字
                    coverage_amount = 0
                    if isinstance(coverage, str):
                        # 尝试从字符串中提取数字
                        numbers = re.findall(r'\d+\.?\d*', coverage)
                        if numbers:
                            coverage_amount = float(numbers[0])

                            # 处理单位（万元、元）
                            if "万" in coverage:
                                coverage_amount *= 10000

                    # 根据产品类型和保额计算年保费
                    annual_premium = 0
                    if "医疗" in product_type:
                        annual_premium = coverage_amount * 0.02
                    elif "重疾" in product_type:
                        annual_premium = coverage_amount * 0.03
                    elif "意外" in product_type:
                        annual_premium = coverage_amount * 0.01
                    elif "寿险" in product_type:
                        annual_premium = coverage_amount * 0.015
                    elif "家财" in product_type:
                        annual_premium = coverage_amount * 0.005
                    elif "教育金" in product_type:
                        annual_premium = coverage_amount * 0.04
                    elif "养老" in product_type:
                        annual_premium = coverage_amount * 0.05
                    elif "长期护理" in product_type:
                        annual_premium = coverage_amount * 0.06
                    else:
                        annual_premium = coverage_amount * 0.02

                    # 添加保费信息到产品
                    product["annual_premium"] = round(annual_premium, 2)

                # 计算总保费
                total_premium = sum(product.get("annual_premium", 0) for product in products)

                return {
                    "products": products,
                    "total_premium": round(total_premium, 2)
                }
          variables:
            - value_selector:
                - match_products_young
                - text
              variable: young_data
            - value_selector:
                - match_products_family
                - text
              variable: family_data
            - value_selector:
                - match_products_parent
                - text
              variable: parent_data
            - value_selector:
                - match_products_middle
                - text
              variable: middle_data
            - value_selector:
                - match_products_retire
                - text
              variable: retire_data

          context:
            enabled: true
            variable_selector:
              - match_products_young
              - text
              - match_products_family
              - text
              - match_products_parent
              - text
              - match_products_middle
              - text
              - match_products_retire
              - text
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: calculate_premium

      - data:
          type: llm
          title: 生成保险方案报告
          model:
            provider: langgenius/openai_api_compatible/openai_api_compatible
            name: glm-4-flashx
            mode: chat
            completion_params:
              temperature: 0.5
          prompt_template:
            - id: system-prompt
              role: system
              text: '{{#context#}}

              你是一位专业的保险顾问，请根据客户信息和推荐的保险产品，生成一份完整的个性化保险方案报告。你必须严格按照指定的格式输出，确保每次生成的报告结构完全一致。不要改变模板的任何部分，包括标题、表格结构、列表格式等。只允许替换模板中的内容部分，保持格式完全一致。'
            - id: user-prompt
              role: user
              text: |
                客户信息：

                年龄：{{#start_node.age#}}
                性别：{{#start_node.gender#}}
                婚姻状况：{{#start_node.marital_status#}}
                子女情况：{{#start_node.children#}}
                职业：{{#start_node.occupation#}}
                月收入：{{#start_node.income#}}元
                现有保险：{{#start_node.existing_insurance#}}
                健康状况：{{#start_node.health_status#}}
                生活方式：{{#start_node.lifestyle#}}
                风险偏好：{{#start_node.risk_preference#}}
                特殊需求：{{#start_node.special_needs#}}

                客户画像分析：
                {{#analyze_customer.text#}}

                推荐的保险产品：
                {{#calculate_premium.products#}}

                总年保费：{{#calculate_premium.total_premium#}}元

                请生成一份完整的个性化保险方案报告，必须严格按照以下格式输出，不允许有任何偏差：

                # 个性化保险方案报告

                ## 1. 客户需求摘要
                客户基本信息：{{#start_node.age#}}岁{{#start_node.gender#}}，职业为{{#start_node.occupation#}}，{{#start_node.marital_status#}}，{{#start_node.children#}}。月收入{{#start_node.income#}}元，现有保险为{{#start_node.existing_insurance#}}。健康状况{{#start_node.health_status#}}，生活方式{{#start_node.lifestyle#}}，风险偏好{{#start_node.risk_preference#}}。

                客户主要保险需求：
                - 需求一：[详细描述]
                - 需求二：[详细描述]
                - 需求三：[详细描述]

                ## 2. 风险分析
                客户面临的主要风险包括：
                - 健康风险：[详细描述健康相关风险]
                - 财务风险：[详细描述财务相关风险]
                - 家庭风险：[详细描述家庭相关风险]
                - 职业风险：[详细描述职业相关风险]
                - 退休风险：[详细描述退休相关风险]

                ## 3. 推荐保险产品组合
                根据客户需求和风险分析，我们推荐以下保险产品组合：

                | 产品类型 | 主要保障内容 |
                |---------|------------|
                | 医疗保险 | [简要描述] |
                | 重疾险 | [简要描述] |
                | 意外险 | [简要描述] |
                | 定期寿险 | [简要描述] |
                | 家财险 | [简要描述] |
                | 养老保险 | [简要描述] |
                | 教育金保险 | [简要描述] |

                ## 4. 产品详情与保费

                | 产品类型 | 保障额度 | 保障期限 | 年保费(元) |
                |---------|---------|---------|-----------|
                | 医疗保险 | [保额] | [期限] | [保费] |
                | 重疾险 | [保额] | [期限] | [保费] |
                | 意外险 | [保额] | [期限] | [保费] |
                | 定期寿险 | [保额] | [期限] | [保费] |
                | 家财险 | [保额] | [期限] | [保费] |
                | 养老保险 | [保额] | [期限] | [保费] |
                | 教育金保险 | [保额] | [期限] | [保费] |

                ## 5. 总体保费预算
                - 总年保费：{{#calculate_premium.total_premium#}}元
                - 月均保费：[计算月均保费]元
                - 占月收入比例：[计算占比]%

                ## 6. 购买优先级建议
                1. 医疗保险：[优先级]，[理由]
                2. 重疾险：[优先级]，[理由]
                3. 意外险：[优先级]，[理由]
                4. 定期寿险：[优先级]，[理由]
                5. 家财险：[优先级]，[理由]
                6. 养老保险：[优先级]，[理由]
                7. 教育金保险：[优先级]，[理由]

                ## 7. 未来保险规划建议
                - 短期（1年内）：[具体建议]
                - 中期（1-3年）：[具体建议]
                - 长期（3-5年）：[具体建议]
                - 健康管理建议：[具体建议]
                - 财务规划建议：[具体建议]

                请使用专业但易于理解的语言，确保每次生成的报告都严格遵循上述格式。不要改变表格结构、列表格式或标题层级。
          context:
            enabled: true
            variable_selector:
              - start_node
              - age
              - gender
              - marital_status
              - children
              - occupation
              - income
              - existing_insurance
              - health_status
              - lifestyle
              - risk_preference
              - special_needs
              - analyze_customer
              - text
              - calculate_premium
              - products
              - total_premium
          outputs:
            text:
              type: string
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: generate_report

      - data:
          type: end
          title: 结束
          outputs:
            - value_selector:
                - generate_report
                - text
              variable: insurance_plan
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: end_node
