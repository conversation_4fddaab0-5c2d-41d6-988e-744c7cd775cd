FROM ubuntu:22.04

# 避免交互式提示
ENV DEBIAN_FRONTEND=noninteractive

# 安装基础依赖
RUN apt-get update && apt-get install -y \
    pandoc \
    python3 \
    python3-pip \
    wget \
    locales \
    fontconfig \
    fonts-wqy-zenhei \
    fonts-wqy-microhei \
    texlive-xetex \
    texlive-lang-chinese \
    texlive-fonts-recommended \
    && rm -rf /var/lib/apt/lists/*

# 设置中文语言环境
RUN locale-gen zh_CN.UTF-8
ENV LANG=zh_CN.UTF-8
ENV LANGUAGE=zh_CN:en
ENV LC_ALL=zh_CN.UTF-8

# 安装宋体字体
RUN mkdir -p /usr/share/fonts/chinese
# 复制字体文件（如果存在）
COPY fonts /usr/share/fonts/chinese/
RUN fc-cache -fv

# 安装Python依赖
COPY requirements.txt /app/
WORKDIR /app
RUN pip3 install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY app.py /app/

# 创建临时目录用于文件处理
RUN mkdir -p /app/temp && chmod 777 /app/temp

# 暴露API端口
EXPOSE 8000

# 启动API服务
CMD ["uvicorn", "app:app", "--host", "0.0.0.0", "--port", "8000"]