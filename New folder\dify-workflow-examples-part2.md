# Dify工作流示例 - 第二部分：中等复杂度工作流

本文档提供了中等复杂度的Dify工作流示例，适用于更复杂的应用场景。

## 1. 客户支持工作流

这个工作流实现了一个客户支持系统，能够分类客户查询，从知识库检索相关信息，并生成个性化回复。

### 用途
适用于客户服务、技术支持等需要结合知识库回答用户问题的场景。

### 完整DSL
```yaml
kind: app
version: "0.1.5"
app:
  mode: workflow
  name: "智能客服助手"
  description: "分类客户查询并提供个性化回复"
  icon: "🧑‍💼"
  icon_background: "#E6FFE6"
  use_icon_as_answer_icon: false
  
  nodes:
    start_node:
      type: start
      title: "开始节点"
      variables: []
      outputs:
        text:
          type: string
      availablePrevNodes: []
      availableNextNodes: []
    
    classify_query:
      type: question-classifier
      title: "查询分类"
      variables: []
      outputs:
        class_name:
          type: string
        class_id:
          type: string
      availablePrevNodes: []
      availableNextNodes: []
    
    check_category:
      type: if-else
      title: "检查类别"
      variable: class_name
      operator: equal
      value: "技术问题"
      varType: string
      variables: []
      outputs:
        case_id:
          type: string
      availablePrevNodes: []
      availableNextNodes: []
    
    search_knowledge:
      type: knowledge
      title: "搜索知识库"
      knowledge_id: "your_knowledge_base_id"
      top_k: 3
      score_threshold: 0.7
      rerank: true
      variables: []
      outputs:
        documents:
          type: array[object]
        query:
          type: string
      availablePrevNodes: []
      availableNextNodes: []
    
    process_knowledge:
      type: code
      title: "处理知识库结果"
      code_language: python3
      code: |
        def main(documents, query) -> dict:
          """处理知识库搜索结果"""
          if not documents or len(documents) == 0:
            return {
              "knowledge_found": False,
              "knowledge_content": "",
              "knowledge_summary": "未找到相关知识"
            }
          
          # 提取文档内容
          contents = []
          for doc in documents:
            if isinstance(doc, dict) and "content" in doc:
              contents.append(doc["content"])
          
          # 合并内容
          combined_content = "\n\n".join(contents)
          
          # 创建摘要
          summary = f"找到 {len(contents)} 条相关知识"
          
          return {
            "knowledge_found": True,
            "knowledge_content": combined_content,
            "knowledge_summary": summary
          }
      variables: []
      outputs:
        knowledge_found:
          type: boolean
        knowledge_content:
          type: string
        knowledge_summary:
          type: string
      availablePrevNodes: []
      availableNextNodes: []
    
    generate_tech_response:
      type: llm
      title: "生成技术回复"
      model:
        provider: langgenius/openai_api_compatible/openai_api_compatible
        name: glm-4-flashx
        mode: chat
      prompt_template:
        - id: system-prompt
          role: system
          text: '{{#context#}}
            你是一个专业的技术支持专家。请根据知识库中的信息回答用户的技术问题。
            如果知识库中没有相关信息，请基于你的知识提供最佳回答，但要明确指出这是基于一般知识而非特定产品文档。
            
            回答时请遵循以下原则：
            1. 使用专业但易懂的语言
            2. 提供具体的步骤和解决方案
            3. 如果需要更多信息，请明确指出
            4. 保持友好、耐心的语气'
        - id: human-prompt
          role: human
          text: '用户问题: {{text}}
            
            知识库信息: {{knowledge_content}}'
      variables: []
      outputs:
        text:
          type: string
      context:
        enabled: true
        variable_selector:
          - start_node
          - text
          - process_knowledge
          - knowledge_content
      availablePrevNodes: []
      availableNextNodes: []
    
    handle_general_query:
      type: llm
      title: "处理一般查询"
      model:
        provider: langgenius/openai_api_compatible/openai_api_compatible
        name: glm-4-flashx
        mode: chat
      prompt_template:
        - id: system-prompt
          role: system
          text: '{{#context#}}
            你是一个友好的客户服务代表。请回答用户的一般性问题，提供有用的信息和建议。
            
            回答时请遵循以下原则：
            1. 使用友好、礼貌的语气
            2. 提供清晰、有用的信息
            3. 如果无法回答特定问题，请提供获取更多帮助的方式
            4. 避免过于技术性的语言'
        - id: human-prompt
          role: human
          text: '{{text}}'
      variables: []
      outputs:
        text:
          type: string
      context:
        enabled: true
        variable_selector:
          - start_node
          - text
      availablePrevNodes: []
      availableNextNodes: []
  
  edges:
    - source: start_node
      target: classify_query
      sourceHandle: ""
      targetHandle: ""
      sourceType: start
      targetType: question-classifier
    
    - source: classify_query
      target: check_category
      sourceHandle: ""
      targetHandle: ""
      sourceType: question-classifier
      targetType: if-else
    
    - source: check_category
      target: search_knowledge
      sourceHandle: ""
      targetHandle: ""
      sourceType: if-else
      targetType: knowledge
    
    - source: check_category
      target: handle_general_query
      sourceHandle: ""
      targetHandle: ""
      sourceType: if-else
      targetType: llm
    
    - source: search_knowledge
      target: process_knowledge
      sourceHandle: ""
      targetHandle: ""
      sourceType: knowledge
      targetType: code
    
    - source: process_knowledge
      target: generate_tech_response
      sourceHandle: ""
      targetHandle: ""
      sourceType: code
      targetType: llm
```

## 2. 内容生成工作流

这个工作流实现了一个内容生成系统，能够根据用户需求生成不同类型的内容，并进行格式化和优化。

### 用途
适用于内容创作、营销文案生成、社交媒体帖子等场景。

### 完整DSL
```yaml
kind: app
version: "0.1.5"
app:
  mode: workflow
  name: "内容创作助手"
  description: "生成各种类型的内容"
  icon: "✍️"
  icon_background: "#FFF0F5"
  use_icon_as_answer_icon: false
  
  nodes:
    start_node:
      type: start
      title: "开始节点"
      variables: []
      outputs:
        text:
          type: string
      availablePrevNodes: []
      availableNextNodes: []
    
    analyze_request:
      type: llm
      title: "分析请求"
      model:
        provider: langgenius/openai_api_compatible/openai_api_compatible
        name: glm-4-flashx
        mode: chat
      prompt_template:
        - id: system-prompt
          role: system
          text: '{{#context#}}
            你是一个内容分析专家。你的任务是分析用户的内容创作请求，提取关键信息。
            请以JSON格式返回以下信息：
            - content_type: 内容类型(blog, social_post, email, ad_copy, other)
            - topic: 主题
            - target_audience: 目标受众
            - tone: 语气(formal, casual, humorous, professional)
            - key_points: 需要包含的要点(数组)
            - length: 内容长度(short, medium, long)'
        - id: human-prompt
          role: human
          text: '请分析以下内容创作请求：{{text}}'
      variables: []
      outputs:
        text:
          type: string
      context:
        enabled: true
        variable_selector:
          - start_node
          - text
      availablePrevNodes: []
      availableNextNodes: []
    
    extract_parameters:
      type: code
      title: "提取参数"
      code_language: python3
      code: |
        import json
        import re
        
        def main(input_data) -> dict:
          """从LLM输出中提取内容参数"""
          try:
            # 尝试从LLM输出中提取JSON
            json_match = re.search(r'```json\s*(.*?)\s*```', input_data, re.DOTALL)
            if json_match:
              data = json.loads(json_match.group(1))
            else:
              # 尝试直接解析整个文本
              try:
                data = json.loads(input_data)
              except:
                # 使用默认值
                return {
                  "content_type": "blog",
                  "topic": "未指定主题",
                  "target_audience": "一般读者",
                  "tone": "professional",
                  "key_points": ["未指定要点"],
                  "length": "medium"
                }
            
            # 确保所有必要字段都存在
            required_fields = ["content_type", "topic", "target_audience", "tone", "key_points", "length"]
            for field in required_fields:
              if field not in data:
                if field == "key_points":
                  data[field] = ["未指定要点"]
                else:
                  data[field] = "未指定"
            
            return data
          except Exception as e:
            # 发生错误时返回默认值
            return {
              "content_type": "blog",
              "topic": "未指定主题",
              "target_audience": "一般读者",
              "tone": "professional",
              "key_points": ["未指定要点"],
              "length": "medium",
              "error": str(e)
            }
      variables: []
      outputs:
        content_type:
          type: string
        topic:
          type: string
        target_audience:
          type: string
        tone:
          type: string
        key_points:
          type: array[string]
        length:
          type: string
      availablePrevNodes: []
      availableNextNodes: []
    
    check_content_type:
      type: if-else
      title: "检查内容类型"
      variable: content_type
      operator: equal
      value: "blog"
      varType: string
      variables: []
      outputs:
        case_id:
          type: string
      availablePrevNodes: []
      availableNextNodes: []
    
    generate_blog:
      type: llm
      title: "生成博客"
      model:
        provider: langgenius/openai_api_compatible/openai_api_compatible
        name: glm-4-flashx
        mode: chat
      prompt_template:
        - id: system-prompt
          role: system
          text: '{{#context#}}
            你是一个专业的博客写作专家。请根据以下参数创作一篇博客文章：
            
            主题：{{topic}}
            目标受众：{{target_audience}}
            语气：{{tone}}
            关键要点：{{key_points}}
            长度：{{length}}
            
            请创作一篇结构完整的博客文章，包含标题、引言、正文和结论。正文应包含所有指定的关键要点。
            根据指定的长度调整内容：
            - short: 300-500字
            - medium: 800-1200字
            - long: 1500-2000字'
        - id: human-prompt
          role: human
          text: '请根据以上参数创作一篇博客文章。'
      variables: []
      outputs:
        text:
          type: string
      context:
        enabled: true
        variable_selector:
          - extract_parameters
          - topic
          - extract_parameters
          - target_audience
          - extract_parameters
          - tone
          - extract_parameters
          - key_points
          - extract_parameters
          - length
      availablePrevNodes: []
      availableNextNodes: []
    
    generate_other_content:
      type: llm
      title: "生成其他内容"
      model:
        provider: langgenius/openai_api_compatible/openai_api_compatible
        name: glm-4-flashx
        mode: chat
      prompt_template:
        - id: system-prompt
          role: system
          text: '{{#context#}}
            你是一个专业的内容创作专家。请根据以下参数创作内容：
            
            内容类型：{{content_type}}
            主题：{{topic}}
            目标受众：{{target_audience}}
            语气：{{tone}}
            关键要点：{{key_points}}
            长度：{{length}}
            
            请根据指定的内容类型创作适当的内容。确保包含所有指定的关键要点，并使用适合目标受众的语言和语气。'
        - id: human-prompt
          role: human
          text: '请根据以上参数创作内容。'
      variables: []
      outputs:
        text:
          type: string
      context:
        enabled: true
        variable_selector:
          - extract_parameters
          - content_type
          - extract_parameters
          - topic
          - extract_parameters
          - target_audience
          - extract_parameters
          - tone
          - extract_parameters
          - key_points
          - extract_parameters
          - length
      availablePrevNodes: []
      availableNextNodes: []
    
    format_content:
      type: code
      title: "格式化内容"
      code_language: python3
      code: |
        def main(content, content_type) -> dict:
          """格式化生成的内容"""
          try:
            # 添加适当的标题
            if content_type == "blog":
              formatted_content = "# 博客文章\n\n" + content
            elif content_type == "social_post":
              formatted_content = "# 社交媒体帖子\n\n" + content
            elif content_type == "email":
              formatted_content = "# 电子邮件\n\n" + content
            elif content_type == "ad_copy":
              formatted_content = "# 广告文案\n\n" + content
            else:
              formatted_content = f"# {content_type.capitalize()}\n\n" + content
            
            # 计算字数
            word_count = len(content.split())
            
            return {
              "formatted_content": formatted_content,
              "word_count": word_count,
              "content_type_display": content_type.replace("_", " ").capitalize()
            }
          except Exception as e:
            return {
              "formatted_content": content,
              "word_count": 0,
              "content_type_display": content_type,
              "error": str(e)
            }
      variables: []
      outputs:
        formatted_content:
          type: string
        word_count:
          type: number
        content_type_display:
          type: string
      availablePrevNodes: []
      availableNextNodes: []
  
  edges:
    - source: start_node
      target: analyze_request
      sourceHandle: ""
      targetHandle: ""
      sourceType: start
      targetType: llm
    
    - source: analyze_request
      target: extract_parameters
      sourceHandle: ""
      targetHandle: ""
      sourceType: llm
      targetType: code
    
    - source: extract_parameters
      target: check_content_type
      sourceHandle: ""
      targetHandle: ""
      sourceType: code
      targetType: if-else
    
    - source: check_content_type
      target: generate_blog
      sourceHandle: ""
      targetHandle: ""
      sourceType: if-else
      targetType: llm
    
    - source: check_content_type
      target: generate_other_content
      sourceHandle: ""
      targetHandle: ""
      sourceType: if-else
      targetType: llm
    
    - source: generate_blog
      target: format_content
      sourceHandle: ""
      targetHandle: ""
      sourceType: llm
      targetType: code
    
    - source: generate_other_content
      target: format_content
      sourceHandle: ""
      targetHandle: ""
      sourceType: llm
      targetType: code
```
