### 测试文件：pandoc-server-tests.http
### 这个文件包含了Pandoc Server API的基本测试请求

@host = http://localhost:8000

### 1. 获取Pandoc版本
GET {{host}}/version
Accept: application/json

### 2. Markdown 转 HTML (使用通用Accept头)
POST {{host}}/
Content-Type: application/json
Accept: */*

{
  "text": "# 你好，世界！\n\n这是一个**粗体**和*斜体*的测试。\n\n## 二级标题\n\n- 列表项1\n- 列表项2\n- 列表项3",
  "from": "markdown",
  "to": "html",
  "standalone": true
}

### 3. HTML 转 Markdown (使用通用Accept头)
POST {{host}}/
Content-Type: application/json
Accept: */*

{
  "text": "<h1>你好，世界！</h1>\n<p>这是一个<strong>粗体</strong>和<em>斜体</em>的测试。</p>\n<h2>二级标题</h2>\n<ul>\n  <li>列表项1</li>\n  <li>列表项2</li>\n  <li>列表项3</li>\n</ul>",
  "from": "html",
  "to": "markdown"
}

### 4. Markdown 转 DOCX (二进制响应)
POST {{host}}/
Content-Type: application/json
Accept: application/octet-stream

{
  "text": "# 周报模板\n\n## 基本信息\n\n- 姓名：[填写您的姓名]\n- 部门：[填写您的部门]\n- 职位：[填写您的职位]\n- 报告周期：[填写日期范围，例如：2024年6月10日-2024年6月16日]\n- 报告提交日期：[填写提交日期]\n\n## 本周工作总结\n\n### 1. 已完成工作\n\n1. [填写已完成的工作项目1，包括完成度、成果等]\n2. [填写已完成的工作项目2，包括完成度、成果等]\n3. [填写已完成的工作项目3，包括完成度、成果等]\n\n### 2. 进行中工作\n\n1. [填写正在进行的工作项目1，包括进度、预计完成时间等]\n2. [填写正在进行的工作项目2，包括进度、预计完成时间等]\n3. [填写正在进行的工作项目3，包括进度、预计完成时间等]\n\n### 3. 工作难点与解决方案\n\n- 难点1：[描述遇到的工作难点或挑战]\n\t- 解决方案：[描述采取的解决措施或方案]\n- 难点2：[描述遇到的工作难点或挑战]\n\t- 解决方案：[描述采取的解决措施或方案]\n\n### 4. 工作成果与亮点\n\n- [描述本周工作中的亮点、突破或创新点]\n- [描述工作成果对团队/项目的贡献]\n\n## 下周工作计划\n\n### 1. 工作目标\n\n- [填写下周的总体工作目标]\n\n### 2. 具体工作计划\n\n1. [填写下周计划完成的工作项目1，包括具体任务、预期目标等]\n2. [填写下周计划完成的工作项目2，包括具体任务、预期目标等]\n3. [填写下周计划完成的工作项目3，包括具体任务、预期目标等]\n\n### 3. 需要协调的资源\n\n- [填写需要团队其他成员协助的事项]\n- [填写需要上级支持的事项]\n- [填写需要其他部门配合的事项]\n\n## 工作反思与建议\n\n### 1. 工作反思\n\n- [对本周工作的总体评价和反思]\n- [对自身工作方法、效率的思考]\n\n### 2. 改进建议\n\n- [对工作流程的改进建议]\n- [对团队协作的改进建议]\n- [对项目管理的改进建议]\n\n## 其他事项\n\n- [填写其他需要汇报的事项]\n- [填写需要特别关注的问题]\n- [填写其他补充信息]\n\n---\n\n*注：请在提交前删除所有方括号中的提示文字，并填写实际内容。*",
  "from": "markdown",
  "to": "docx",
  "standalone": true
}

### 保存DOCX到本地文件（使用curl）
# curl -X POST http://localhost:8000/ \
#   -H "Content-Type: application/json" \
#   -H "Accept: application/octet-stream" \
#   -d '{
#     "text": "# 你好，世界！\n\n这是一个**粗体**和*斜体*的测试。",
#     "from": "markdown",
#     "to": "docx",
#     "standalone": true
#   }' \
#   --output output.docx

### 5. 批量转换 - 使用/batch端点
POST {{host}}/batch
Content-Type: application/json
Accept: */*

[
  {
    "text": "# 文档1\n\n这是第一个文档。",
    "from": "markdown",
    "to": "html"
  },
  {
    "text": "# 文档2\n\n这是第二个文档。",
    "from": "markdown",
    "to": "html"
  }
]

### 6. 尝试生成PDF (通过latex)
POST {{host}}/
Content-Type: application/json
Accept: */*

{
  "text": "# 你好，世界！\n\n这是一个**粗体**和*斜体*的测试。",
  "from": "markdown",
  "to": "latex",
  "output-file": "output.pdf",
  "standalone": true
}

### 7. 尝试生成PDF (通过html5)
POST {{host}}/
Content-Type: application/json
Accept: */*

{
  "text": "# 你好，世界！\n\n这是一个**粗体**和*斜体*的测试。",
  "from": "markdown",
  "to": "html5",
  "output-file": "output.pdf",
  "standalone": true
}

### 8. Word(DOCX)转Markdown - 使用curl命令（推荐方式）
# 注意：这个命令需要在终端中执行，不能直接在HTTP文件中运行
# curl -X POST http://localhost:8000/ \
#   -F "from=docx" \
#   -F "to=markdown" \
#   -F "file=@/path/to/your/document.docx" \
#   --output output.md

### 9. Word(DOCX)转Markdown - 使用multipart/form-data（推荐方式）
# 注意：这个请求需要在Postman或Apifox等工具中执行，.http文件对二进制文件支持有限
# POST {{host}}/
# Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW
#
# ------WebKitFormBoundary7MA4YWxkTrZu0gW
# Content-Disposition: form-data; name="from"
#
# docx
# ------WebKitFormBoundary7MA4YWxkTrZu0gW
# Content-Disposition: form-data; name="to"
#
# markdown
# ------WebKitFormBoundary7MA4YWxkTrZu0gW
# Content-Disposition: form-data; name="file"; filename="document.docx"
# Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document
#
# [二进制文件内容]
# ------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 10. Word(DOCX)转Markdown - 使用文本内容（仅适用于简单Word文件，不推荐）
POST {{host}}/
Content-Type: application/json
Accept: */*

{
  "text": "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><w:document xmlns:w=\"http://schemas.openxmlformats.org/wordprocessingml/2006/main\"><w:body><w:p><w:r><w:t>这是一个简单的Word文档内容示例</w:t></w:r></w:p></w:body></w:document>",
  "from": "docx",
  "to": "markdown"
}
