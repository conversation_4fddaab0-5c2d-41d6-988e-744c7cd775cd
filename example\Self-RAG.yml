app:
  description: Self RAG：通过LLM反思自身输出，并不断迭代优化回答，增强回答的连贯性、准确性和相关性。
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: 【测试】Self-RAG
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/siliconflow:0.0.8@217f973bd7ced1b099c2f0c669f1356bdf4cc38b8372fd58d7874f9940b95de3
kind: app
version: 0.1.5
workflow:
  conversation_variables:
  - description: 最后结果
    id: 7f5820d2-07a4-473a-bcc8-22b6ffa2a56f
    name: finallAnswer
    selector:
    - conversation
    - finallAnswer
    value: ''
    value_type: string
  - description: 评分为YES的chunks
    id: 9d65dcba-98d9-4345-a7f5-0eea6423e309
    name: filtered_docs
    selector:
    - conversation
    - filtered_docs
    value: ''
    value_type: string
  - description: 基于原始问题进行重写。
    id: 185c140e-c763-4154-9e4a-9763d5dbc606
    name: reQuery
    selector:
    - conversation
    - reQuery
    value: ''
    value_type: string
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: loop
      id: 1744610481918-source-1744614444681-target
      source: '1744610481918'
      sourceHandle: source
      target: '1744614444681'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: code
        targetType: knowledge-retrieval
      id: 1744614486889-source-1744614478508-target
      source: '1744614486889'
      sourceHandle: source
      target: '1744614478508'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: knowledge-retrieval
        targetType: code
      id: 1744614478508-source-1744615311611-target
      source: '1744614478508'
      sourceHandle: source
      target: '1744615311611'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: code
        targetType: llm
      id: 1744615311611-source-1744615075452-target
      source: '1744615311611'
      sourceHandle: source
      target: '1744615075452'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: llm
        targetType: assigner
      id: 1744616236018-source-1744616409947-target
      source: '1744616236018'
      sourceHandle: source
      target: '1744616409947'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: llm
        targetType: llm
      id: 1744616339334-source-1744616451373-target
      source: '1744616339334'
      sourceHandle: source
      target: '1744616451373'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: llm
        targetType: if-else
      id: 1744616451373-source-1744616501416-target
      source: '1744616451373'
      sourceHandle: source
      target: '1744616501416'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: llm
        targetType: if-else
      id: 1744616675645-source-1744616720788-target
      source: '1744616675645'
      sourceHandle: source
      target: '1744616720788'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: if-else
        targetType: llm
      id: 1744616501416-false-1744616675645-target
      source: '1744616501416'
      sourceHandle: 'false'
      target: '1744616675645'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: if-else
        targetType: llm
      id: 1744616720788-true-1744616236018-target
      source: '1744616720788'
      sourceHandle: 'true'
      target: '1744616236018'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: loop-start
        targetType: if-else
      id: 1744614444681start-source-1744617750070-target
      source: 1744614444681start
      sourceHandle: source
      target: '1744617750070'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: if-else
        targetType: code
      id: 1744617750070-true-1744614486889-target
      source: '1744617750070'
      sourceHandle: 'true'
      target: '1744614486889'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: if-else
        targetType: llm
      id: 1744617750070-false-1744616339334-target
      source: '1744617750070'
      sourceHandle: 'false'
      target: '1744616339334'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: assigner
        targetType: llm
      id: 1744618887928-source-1744616339334-target
      source: '1744618887928'
      sourceHandle: source
      target: '1744616339334'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: if-else
        targetType: assigner
      id: 1744616501416-true-1744619138669-target
      source: '1744616501416'
      sourceHandle: 'true'
      target: '1744619138669'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: if-else
        targetType: assigner
      id: 1744616720788-false-1744619976993-target
      source: '1744616720788'
      sourceHandle: 'false'
      target: '1744619976993'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: loop
        targetType: if-else
      id: 1744614444681-source-1744620253642-target
      source: '1744614444681'
      sourceHandle: source
      target: '1744620253642'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: if-else
        targetType: answer
      id: 1744620253642-true-1744620274008-target
      source: '1744620253642'
      sourceHandle: 'true'
      target: '1744620274008'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: if-else
        targetType: answer
      id: 1744620253642-false-1744620284607-target
      source: '1744620253642'
      sourceHandle: 'false'
      target: '1744620284607'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        sourceType: llm
        targetType: assigner
      id: 1744615075452-source-1744618887928-target
      source: '1744615075452'
      sourceHandle: source
      target: '1744618887928'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: answer
        targetType: assigner
      id: 1744620284607-source-1744678749249-target
      source: '1744620284607'
      sourceHandle: source
      target: '1744678749249'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: answer
        targetType: assigner
      id: 1744620274008-source-1744678749249-target
      source: '1744620274008'
      sourceHandle: source
      target: '1744678749249'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables: []
      height: 52
      id: '1744610481918'
      position:
        x: 30
        y: 720.5
      positionAbsolute:
        x: 30
        y: 720.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        break_conditions:
        - comparison_operator: not empty
          id: 3b8519cbf4224f07b8e10544934fbac7
          value: ''
          varType: string
          variable_selector:
          - conversation
          - finallAnswer
        desc: ''
        error_handle_mode: terminated
        height: 1041
        logical_operator: and
        loop_count: 3
        selected: false
        start_node_id: 1744614444681start
        title: 循环
        type: loop
        width: 3012
      height: 1041
      id: '1744614444681'
      position:
        x: 332
        y: 720.5
      positionAbsolute:
        x: 332
        y: 720.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 3012
      zIndex: 1
    - data:
        desc: ''
        isInLoop: true
        selected: false
        title: ''
        type: loop-start
      draggable: false
      height: 48
      id: 1744614444681start
      parentId: '1744614444681'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 356
        y: 788.5
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-loop-start
      width: 44
      zIndex: 1002
    - data:
        dataset_ids:
        - 2250ed95-a817-436a-b1d0-5c04baea5cc3
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        multiple_retrieval_config:
          reranking_enable: false
          reranking_mode: reranking_model
          reranking_model:
            model: netease-youdao/bce-reranker-base_v1
            provider: langgenius/siliconflow/siliconflow
          score_threshold: null
          top_k: 5
        query_variable_selector:
        - '1744614486889'
        - result
        retrieval_mode: multiple
        selected: false
        title: 知识检索
        type: knowledge-retrieval
      height: 90
      id: '1744614478508'
      parentId: '1744614444681'
      position:
        x: 557.1956786080493
        y: 195.092842645666
      positionAbsolute:
        x: 889.1956786080493
        y: 915.592842645666
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        code: "\ndef main(arg1: str, arg2: str) -> dict:\n    result = arg2 if arg2\
          \ else arg1\n    return {\n        \"result\": result,\n    }\n"
        code_language: python3
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 代码 query转换
        type: code
        variables:
        - value_selector:
          - sys
          - query
          variable: arg1
        - value_selector:
          - conversation
          - reQuery
          variable: arg2
      height: 52
      id: '1744614486889'
      parentId: '1744614444681'
      position:
        x: 437.037852857689
        y: 65
      positionAbsolute:
        x: 769.037852857689
        y: 785.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-7B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 1265245f-dbb6-48cb-975c-1e5ff0413e2f
          role: system
          text: '您是一名评分专家。您将获得一个问题和多个事实文档。您需要对问题和每个事实文档的相关性进行分别评分，分数为0-10分，十分是最高（最佳）分数。0分是您可以给出的最低分数。

            问题： {{#sys.query#}}

            多个事实文档：{{#1744615311611.result#}}

            最后，您需要根据文档数量以及质量，将评分较高的文档以及其对应的内容挨个换行输出。没有可预定项或解释，不要说其他的话。'
        selected: false
        title: LLM 文档评分
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744615075452'
      parentId: '1744614444681'
      position:
        x: 718.0302708470128
        y: 506.3759863068642
      positionAbsolute:
        x: 1050.0302708470128
        y: 1226.8759863068642
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        code: "def main(arg: []) -> dict:\n    formatted_data = []\n    for idx, item\
          \ in enumerate(arg, 1):\n        content = item.get('content')\n       \
          \ formatted_data.append(f\"文档{idx}. {content}\")\n    \n    result_str =\
          \ \"\\n\\n\".join(formatted_data)\n    return {\"result\": result_str}\n"
        code_language: python3
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 代码 文档处理
        type: code
        variables:
        - value_selector:
          - '1744614478508'
          - result
          variable: arg
      height: 52
      id: '1744615311611'
      parentId: '1744614444681'
      position:
        x: 618.7160866191161
        y: 369.4198167119764
      positionAbsolute:
        x: 950.7160866191161
        y: 1089.9198167119764
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-7B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: d8c1da31-81f0-4f42-bd1f-e6b423fef367
          role: system
          text: '您是一个问题重写器，可将输入问题转换为经过优化的更好版本，用于 VectorStore 检索。查看输入问题并尝试推理潜在的语义意图/含义。

            以下是需要优化的初始问题：{{#sys.query#}}

            请生成语义表达更完整、检索效果更佳的改进版本。'
        selected: false
        title: LLM 问题重写
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744616236018'
      parentId: '1744614444681'
      position:
        x: 2419.4746001518547
        y: 170.42048701149088
      positionAbsolute:
        x: 2751.4746001518547
        y: 890.9204870114909
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-72B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 99c6ac98-846e-4c21-9b18-8c290a19d527
          role: system
          text: '请根据以下检索到的文档内容和问题生成一个详细且准确的回答，如果文档内容与问题无关，请回答不知道。

            文档内容：{{#conversation.filtered_docs#}}

            问题：{{#sys.query#}}

            回答：'
        selected: false
        title: LLM 生成答案
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744616339334'
      parentId: '1744614444681'
      position:
        x: 899.5679973256308
        y: 872.4438283143722
      positionAbsolute:
        x: 1231.5679973256308
        y: 1592.9438283143722
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1744616236018'
          - text
          variable_selector:
          - conversation
          - reQuery
          write_mode: over-write
        - input_type: variable
          operation: clear
          value: ''
          variable_selector:
          - conversation
          - filtered_docs
          write_mode: over-write
        loop_id: '1744614444681'
        selected: false
        title: 变量赋值 3
        type: assigner
        version: '2'
      height: 114
      id: '1744616409947'
      parentId: '1744614444681'
      position:
        x: 2713.8277682912158
        y: 151.5532678219979
      positionAbsolute:
        x: 3045.8277682912158
        y: 872.0532678219979
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-7B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: b2579513-ca9e-4582-932d-80c7a9c677a7
          role: system
          text: '您是一名评分员，负责评估LLM的生成结果是否是以一组检索到的事实文档为基础或者得到支持。

            给出分数 ''yes'' 或 ''no''。yes 意味着生成结果以一组事实为基础或者得到支持。no 代表事实文档并不能支持该生成结果。

            yes 是最高（最佳）分数。no 是您可以给出的最低分数。

            生成结果： {{#1744616339334.text#}}

            事实文档：{{#conversation.filtered_docs#}}

            只需要回答 ''yes'' 或 ''no'' 分数，以指示文档是否能支持生成结果，没有可预定项或解释，不要说其他的话。'
        selected: false
        title: LLM 幻觉判断
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744616451373'
      parentId: '1744614444681'
      position:
        x: 1192.5384882572682
        y: 754.3588876135304
      positionAbsolute:
        x: 1524.5384882572682
        y: 1474.8588876135304
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: is not
            id: bc4bcbec-ce33-4fc0-a959-b1eec2e706c2
            value: 'yes'
            varType: string
            variable_selector:
            - '1744616451373'
            - text
          id: 'true'
          logical_operator: and
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        selected: false
        title: 条件分支 幻觉结果
        type: if-else
      height: 124
      id: '1744616501416'
      parentId: '1744614444681'
      position:
        x: 1478.6764531629083
        y: 596.1301064888466
      positionAbsolute:
        x: 1810.6764531629083
        y: 1316.6301064888466
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen2.5-7B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 94eadf88-b7fc-41ac-9cc0-d128905d7cb1
          role: system
          text: '您是一名评分员，负责评估LLM的生成结果是否解决了用户的问题。

            给出分数 ''yes'' 或 ''no''。yes 代表着生成结果可以解决问题。no 代表生成结果不能解决问题

            yes 是最高（最佳）分数。no 是您可以给出的最低分数。

            生成结果： {{#1744616339334.text#}}

            用户的问题：{{#sys.query#}}

            只需要回答 ''yes'' 或 ''no'' 分数，以指示文档是否能支持生成结果，没有可预定项或解释，不要说其他的话。'
        selected: false
        title: LLM 答案检验
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744616675645'
      parentId: '1744614444681'
      position:
        x: 1777.7751731329327
        y: 778.1314147074238
      positionAbsolute:
        x: 2109.7751731329327
        y: 1498.6314147074238
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: is not
            id: 4da2419a-b0de-474a-8ff9-72788fa07687
            value: 'yes'
            varType: string
            variable_selector:
            - '1744616675645'
            - text
          id: 'true'
          logical_operator: and
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        selected: false
        title: 条件分支 答案结果
        type: if-else
      height: 124
      id: '1744616720788'
      parentId: '1744614444681'
      position:
        x: 2106.537359747895
        y: 696.0916335995605
      positionAbsolute:
        x: 2438.537359747895
        y: 1416.5916335995605
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        author: tanchg
        desc: ''
        height: 473
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          14px;","text":"逻辑误差","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          14px;","text":"由于dify不能在循环内嵌套循环，导致以下误差：","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          14px;","text":"1.原本评分逻辑是对单个chunk进行评分，然后保留合格的chunk，如果没有合格chunk，则重写query再召回。","type":"text","version":1},{"type":"linebreak","version":1},{"detail":0,"format":0,"mode":"normal","style":"font-size:
          14px;","text":"此处改为对所有chunk进行分别评分，返回评分最高的chunk。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[],"direction":null,"format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          14px;","text":"2.没有内部循环后，幻觉检验的内部循环生成和答案合理性检验的内部循环query重写只能在外部进行循环，会占用外部循环次数。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: blue
        title: ''
        type: ''
        width: 446
      height: 473
      id: '1744617116255'
      position:
        x: -129.77943187067547
        y: 1005.9785580054715
      positionAbsolute:
        x: -129.77943187067547
        y: 1005.9785580054715
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 446
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: empty
            id: 33f49369-33dd-4a00-b689-e8642a1d6c55
            value: ''
            varType: array[string]
            variable_selector:
            - conversation
            - filtered_docs
          id: 'true'
          logical_operator: and
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744614444681'
        selected: false
        title: 条件分支 循环生成
        type: if-else
      height: 124
      id: '1744617750070'
      parentId: '1744614444681'
      position:
        x: 131.89513582567338
        y: 65
      positionAbsolute:
        x: 463.8951358256734
        y: 785.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1744615075452'
          - text
          variable_selector:
          - conversation
          - filtered_docs
          write_mode: over-write
        loop_id: '1744614444681'
        selected: false
        title: 变量赋值 2
        type: assigner
        version: '2'
      height: 86
      id: '1744618887928'
      parentId: '1744614444681'
      position:
        x: 855.5003222497692
        y: 672.9656668612822
      positionAbsolute:
        x: 1187.5003222497692
        y: 1393.4656668612822
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        items:
        - input_type: variable
          operation: clear
          value: ''
          variable_selector:
          - conversation
          - reQuery
          write_mode: over-write
        loop_id: '1744614444681'
        selected: false
        title: 变量赋值 3
        type: assigner
        version: '2'
      height: 86
      id: '1744619138669'
      parentId: '1744614444681'
      position:
        x: 1747.3662946738018
        y: 424.5659864766601
      positionAbsolute:
        x: 2079.366294673802
        y: 1145.06598647666
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1744616339334'
          - text
          variable_selector:
          - conversation
          - finallAnswer
          write_mode: over-write
        loop_id: '1744614444681'
        selected: false
        title: 变量赋值 4
        type: assigner
        version: '2'
      height: 86
      id: '1744619976993'
      parentId: '1744614444681'
      position:
        x: 2437.4805771402835
        y: 748.5912850691323
      positionAbsolute:
        x: 2769.4805771402835
        y: 1469.0912850691323
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
      zIndex: 1002
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: empty
            id: 7cb087b3-5ac5-4421-8cf0-d61a0f7dec05
            value: ''
            varType: string
            variable_selector:
            - conversation
            - finallAnswer
          id: 'true'
          logical_operator: and
        desc: ''
        selected: false
        title: 条件分支 结果判定
        type: if-else
      height: 124
      id: '1744620253642'
      position:
        x: 3404
        y: 720.5
      positionAbsolute:
        x: 3404
        y: 720.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        answer: 抱歉，我不知道
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 100
      id: '1744620274008'
      position:
        x: 3710.1992413722323
        y: 701.3790175121287
      positionAbsolute:
        x: 3710.1992413722323
        y: 701.3790175121287
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        answer: '{{#conversation.finallAnswer#}}'
        desc: ''
        selected: false
        title: 直接回复 2
        type: answer
        variables: []
      height: 103
      id: '1744620284607'
      position:
        x: 3724.8965861750435
        y: 844.9772411669577
      positionAbsolute:
        x: 3724.8965861750435
        y: 844.9772411669577
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: clear
          value: ''
          variable_selector:
          - conversation
          - finallAnswer
          write_mode: over-write
        - input_type: variable
          operation: clear
          value: ''
          variable_selector:
          - conversation
          - filtered_docs
          write_mode: over-write
        - input_type: variable
          operation: clear
          value: ''
          variable_selector:
          - conversation
          - reQuery
          write_mode: over-write
        selected: false
        title: 变量赋值 5
        type: assigner
        version: '2'
      height: 142
      id: '1744678749249'
      position:
        x: 4062.5901378390154
        y: 760.3927930362033
      positionAbsolute:
        x: 4062.5901378390154
        y: 760.3927930362033
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        author: tanchg
        desc: ''
        height: 145
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"Self RAG：包含一个轻量级检索评估器来评估检索到的文档的质量和相关性，同时通过LLM反思自身生成的结果是否具有幻觉以及能否回答用户问题，并不断迭代优化回答，增强回答的连贯性、准确性和相关性。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":"font-size:
          16px;"}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: blue
        title: ''
        type: ''
        width: 545
      height: 145
      id: '1744698578690'
      position:
        x: 332
        y: 536.5236850397862
      positionAbsolute:
        x: 332
        y: 536.5236850397862
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 545
    viewport:
      x: 171.57302180556144
      y: -257.75477250455515
      zoom: 0.8572440108367627
