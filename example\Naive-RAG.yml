app:
  description: 朴素RAG：结合检索系统和生成模型，使LLM能够动态访问知识库，生成高质量、上下文相关的响应
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: 【测试】Naive-RAG
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/siliconflow:0.0.8@217f973bd7ced1b099c2f0c669f1356bdf4cc38b8372fd58d7874f9940b95de3
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: knowledge-retrieval
      id: 1744182759966-source-1744183351791-target
      selected: false
      source: '1744182759966'
      sourceHandle: source
      target: '1744183351791'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: end
      id: 1744183381184-source-1744184245035-target
      selected: false
      source: '1744183381184'
      sourceHandle: source
      target: '1744184245035'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: knowledge-retrieval
        targetType: llm
      id: 1744183351791-source-1744183381184-target
      source: '1744183351791'
      sourceHandle: source
      target: '1744183381184'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: query
          max_length: 48
          options: []
          required: true
          type: text-input
          variable: query
      height: 88
      id: '1744182759966'
      position:
        x: 30
        y: 245
      positionAbsolute:
        x: 30
        y: 245
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        dataset_ids:
        - 2250ed95-a817-436a-b1d0-5c04baea5cc3
        desc: ''
        multiple_retrieval_config:
          reranking_enable: true
          reranking_mode: reranking_model
          reranking_model:
            model: netease-youdao/bce-reranker-base_v1
            provider: langgenius/siliconflow/siliconflow
          top_k: 4
        query_variable_selector:
        - '1744182759966'
        - query
        retrieval_mode: multiple
        selected: false
        title: 知识检索
        type: knowledge-retrieval
      height: 90
      id: '1744183351791'
      position:
        x: 332
        y: 245
      positionAbsolute:
        x: 332
        y: 245
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        context:
          enabled: true
          variable_selector:
          - '1744183351791'
          - result
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: Qwen/QVQ-72B-Preview
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 042b208c-4fae-4c02-b70c-d8e01a29df22
          role: system
          text: '请根据以下检索到的文档内容和问题生成一个详细且准确的回答，如果文档内容与问题无关，请回答不知道。

            文档内容：{{#context#}}

            问题：{{#1744182759966.query#}}

            回答：'
        selected: false
        title: LLM 2
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '1744183381184'
      position:
        x: 634
        y: 245
      positionAbsolute:
        x: 634
        y: 245
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1744183381184'
          - text
          variable: answer
        selected: false
        title: 结束
        type: end
      height: 88
      id: '1744184245035'
      position:
        x: 936
        y: 245
      positionAbsolute:
        x: 936
        y: 245
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        author: tanchg
        desc: ''
        height: 103
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"朴素RAG：最基础的检索增强生成，通过整合外部知识库来增强大型语言模型（LLM）性能","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":"font-size:
          16px;"}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: blue
        title: ''
        type: ''
        width: 422
      height: 103
      id: '1744697544544'
      position:
        x: 35.28738752478995
        y: 363.6067244212488
      positionAbsolute:
        x: 35.28738752478995
        y: 363.6067244212488
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 422
    viewport:
      x: 276.7587873191916
      y: 113.85575078547436
      zoom: 1.1111807499801696
