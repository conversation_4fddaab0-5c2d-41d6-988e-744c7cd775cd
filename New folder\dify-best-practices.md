# Dify工作流最佳实践

## 1. 工作流设计原则

### 1.1 模块化设计
- 将复杂功能拆分为多个专注于单一任务的节点
- 使用有意义的节点名称和标题，便于理解和维护
- 避免创建过于复杂的单个节点，特别是代码节点

### 1.2 数据流清晰
- 确保数据流向清晰可见
- 避免复杂的交叉连接
- 使用条件节点明确分支逻辑

### 1.3 错误处理
- 在关键节点添加错误处理逻辑
- 使用代码节点验证输入数据
- 为异常情况设计备用路径

### 1.4 可维护性
- 为节点添加清晰的注释和说明
- 使用一致的命名约定
- 定期测试和优化工作流

## 2. 节点配置最佳实践

### 2.1 开始节点
- 保持开始节点简单，仅用于接收初始输入
- 不要在开始节点添加复杂逻辑
- 示例配置：
```yaml
start_node:
  type: start
  title: "开始节点"
  variables: []
  outputs:
    text:
      type: string
  availablePrevNodes: []
  availableNextNodes: []
```

### 2.2 LLM节点
- 编写清晰、具体的提示词
- 使用系统提示词设置角色和行为边界
- 在需要访问其他节点数据时启用上下文
- 示例配置：
```yaml
analyze_input:
  type: llm
  title: "分析用户输入"
  model:
    provider: langgenius/openai_api_compatible/openai_api_compatible
    name: glm-4-flashx
    mode: chat
  prompt_template:
    - id: system-prompt
      role: system
      text: '{{#context#}}
        你是一个专业的分析师。你的任务是分析用户输入并提取关键信息。
        请以JSON格式返回结果，包含以下字段：
        - main_topic: 主要话题
        - key_points: 关键点列表
        - sentiment: 情感倾向(positive/neutral/negative)'
    - id: human-prompt
      role: human
      text: '请分析以下文本：{{text}}'
  variables: []
  outputs:
    text:
      type: string
  context:
    enabled: true
    variable_selector:
      - start_node
      - text
  availablePrevNodes: []
  availableNextNodes: []
```

### 2.3 代码节点
- 添加充分的注释和错误处理
- 使用函数模块化代码
- 验证输入数据并处理异常情况
- 示例配置：
```yaml
process_data:
  type: code
  title: "处理数据"
  code_language: python3
  code: |
    import json
    import re
    
    def main(input_data) -> dict:
      """
      处理输入数据并返回结构化结果
      
      Args:
          input_data: 输入数据，通常是LLM节点的输出
          
      Returns:
          dict: 包含处理结果的字典
      """
      try:
        # 尝试解析JSON
        if isinstance(input_data, str):
          # 从文本中提取JSON部分
          json_match = re.search(r'```json\s*(.*?)\s*```', input_data, re.DOTALL)
          if json_match:
            data = json.loads(json_match.group(1))
          else:
            # 尝试直接解析整个文本
            try:
              data = json.loads(input_data)
            except:
              # 如果解析失败，返回错误信息
              return {
                "processed_data": [],
                "status": "error",
                "message": "无法解析输入数据"
              }
        elif isinstance(input_data, dict):
          data = input_data
        else:
          return {
            "processed_data": [],
            "status": "error",
            "message": "输入数据类型不支持"
          }
        
        # 处理数据
        processed_data = process_items(data)
        
        return {
          "processed_data": processed_data,
          "status": "success",
          "message": "数据处理成功"
        }
      except Exception as e:
        # 捕获所有异常
        return {
          "processed_data": [],
          "status": "error",
          "message": f"处理过程中出错: {str(e)}"
        }
    
    def process_items(data):
      """处理数据项的辅助函数"""
      # 实际处理逻辑
      return data
  variables: []
  outputs:
    processed_data:
      type: array[object]
    status:
      type: string
    message:
      type: string
  availablePrevNodes: []
  availableNextNodes: []
```

### 2.4 条件节点
- 使用明确的条件表达式
- 确保条件变量存在且类型正确
- 为每个分支添加清晰的后续处理
- 示例配置：
```yaml
check_status:
  type: if-else
  title: "检查状态"
  variable: status
  operator: equal
  value: "success"
  varType: string
  variables: []
  outputs:
    case_id:
      type: string
  availablePrevNodes: []
  availableNextNodes: []
```

## 3. 变量和上下文管理

### 3.1 变量命名约定
- 使用描述性名称，如`customer_type`而非`ct`
- 使用小写字母和下划线分隔单词
- 保持命名一致性

### 3.2 上下文配置
- 只选择必要的变量，避免过度使用上下文
- 在系统提示词开头使用`{{#context#}}`标记
- 示例配置：
```yaml
context:
  enabled: true
  variable_selector:
    - start_node
    - text
    - analyze_input
    - text
```

### 3.3 变量映射
- 使用明确的输入/输出映射
- 确保源变量和目标变量类型匹配
- 示例配置：
```yaml
input_mapping:
  - source_selector:
      - analyze_input
      - text
    target: input_data
```

## 4. 工作流模式和结构

### 4.1 线性工作流
适用于顺序处理步骤，如：
1. 开始节点 → 2. 输入分析(LLM) → 3. 数据处理(代码) → 4. 结果生成(LLM)

### 4.2 分支工作流
适用于需要条件处理的场景：
1. 开始节点 → 2. 问题分类 → 3A. 处理类型A / 3B. 处理类型B → 4. 结果合并

### 4.3 循环工作流
通过代码节点实现循环处理：
```python
def main(input_data) -> dict:
  # 处理第一项
  if "continue_processing" in input_data and input_data["continue_processing"]:
    # 继续处理下一项
    next_index = input_data.get("current_index", 0) + 1
    return {
      "current_index": next_index,
      "continue_processing": next_index < len(input_data.get("items", [])),
      "current_item": input_data.get("items", [])[next_index] if next_index < len(input_data.get("items", [])) else None
    }
  else:
    # 初始化处理
    items = get_items_to_process()
    return {
      "current_index": 0,
      "continue_processing": len(items) > 0,
      "current_item": items[0] if items else None,
      "items": items
    }
```

## 5. 性能优化

### 5.1 减少LLM调用
- 合并可以一次完成的任务
- 使用代码节点处理结构化数据
- 避免不必要的重复生成

### 5.2 优化提示词
- 提供清晰、具体的指令
- 使用结构化输出格式
- 限制生成的内容长度

### 5.3 缓存和重用
- 缓存频繁使用的数据
- 重用计算结果
- 使用代码节点存储中间状态

## 6. 调试和测试

### 6.1 增量开发
- 从简单工作流开始，逐步添加功能
- 每添加一个节点就测试
- 使用简单输入验证基本功能

### 6.2 日志和监控
- 在代码节点添加日志输出
- 监控关键节点的输入和输出
- 记录异常情况

### 6.3 测试策略
- 测试边界条件和异常情况
- 使用多样化的输入测试工作流
- 验证最终输出是否符合预期
