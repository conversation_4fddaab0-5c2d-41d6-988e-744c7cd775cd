app:
  description: "智能化开发辅助系统，支持从需求分析到测试用例的完整开发文档生成流程"
  icon: "🚀"
  icon_background: "#E3F2FD"
  mode: advanced-chat
  name: 开发辅助系统-简化版
  use_icon_as_answer_icon: false

dependencies:
  - current_identifier: null
    type: marketplace
    value:
      marketplace_plugin_unique_identifier: langgenius/siliconflow:0.0.12@2ab1fcd77138b7ecdd707790aa1936d5c187fca547ebd165728237b0630c3a44

kind: app
version: 0.1.0

workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
        - .doc
        - .docx
        - .pdf
        - .txt
        - .md
      allowed_file_types:
        - document
      allowed_file_upload_methods:
        - local_file
      enabled: true
      number_limits: 3
    opening_statement: "欢迎使用开发辅助系统！我可以帮您从需求分析到测试用例生成完整的开发文档。"
    suggested_questions:
      - "我想生成一个电商系统的开发文档"
      - "帮我设计一个用户管理系统"
      - "生成API接口文档"
    suggested_questions_after_answer:
      enabled: true
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    text_to_speech:
      enabled: false

  graph:
    nodes:
      # 开始节点
      - data:
          desc: "定义项目基本信息"
          selected: false
          title: 开始
          type: start
          variables:
            - label: 项目名称
              max_length: 100
              required: true
              type: text-input
              variable: project_name
            - label: 项目类型
              options:
                - web应用
                - 移动应用
                - API服务
                - 桌面应用
              required: true
              type: select
              variable: project_type
            - label: 需求描述
              max_length: 2000
              required: true
              type: paragraph
              variable: requirements_input
        height: 120
        id: "start"
        position:
          x: 30
          y: 300
        positionAbsolute:
          x: 30
          y: 300
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 280

      # 需求分析节点
      - data:
          desc: "分析需求并生成结构化文档"
          context:
            enabled: false
            variable_selector: []
          model:
            completion_params:
              temperature: 0.3
              max_tokens: 3000
            mode: chat
            name: Pro/deepseek-ai/DeepSeek-V3
            provider: langgenius/siliconflow/siliconflow
          prompt_template:
            - id: "requirements-analysis"
              role: system
              text: |
                你是一个专业的需求分析师。请将以下原始需求转换为结构化的需求文档：

                项目名称：{{start.project_name}}
                项目类型：{{start.project_type}}
                原始需求：{{start.requirements_input}}

                请按以下格式输出：

                ## 功能需求
                - FR001: [功能名称] - [详细描述]
                - FR002: [功能名称] - [详细描述]

                ## 非功能需求
                - NFR001: [类型] - [具体要求]
                - NFR002: [类型] - [具体要求]

                ## 用户角色
                - 角色1：[角色描述]
                - 角色2：[角色描述]

                请确保需求描述清晰、具体、可测量。
          selected: false
          title: 需求分析
          type: llm
          variables: []
          vision:
            enabled: false
        height: 54
        id: "requirements-analysis"
        position:
          x: 380
          y: 300
        positionAbsolute:
          x: 380
          y: 300
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244

      # 功能清单生成节点
      - data:
          desc: "生成详细功能清单"
          context:
            enabled: false
            variable_selector: []
          model:
            completion_params:
              temperature: 0.3
              max_tokens: 3000
            mode: chat
            name: Pro/deepseek-ai/DeepSeek-V3
            provider: langgenius/siliconflow/siliconflow
          prompt_template:
            - id: "function-list"
              role: system
              text: |
                基于以下需求分析结果，生成功能清单：

                需求分析结果：
                {{requirements-analysis.text}}

                请按以下格式输出功能清单：

                ## 功能清单

                ### 模块：[模块名称]
                - UC001: [功能名称] [P0] - [详细描述和验收标准]
                - UC002: [功能名称] [P1] - [详细描述和验收标准]

                优先级说明：
                - P0: 核心功能，必须实现
                - P1: 重要功能，优先实现
                - P2: 一般功能，后续实现

                请确保功能编号唯一且有规律，功能描述包含验收标准。
          selected: false
          title: 功能清单生成
          type: llm
          variables: []
          vision:
            enabled: false
        height: 54
        id: "function-list"
        position:
          x: 680
          y: 300
        positionAbsolute:
          x: 680
          y: 300
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244

      # 数据库设计节点
      - data:
          desc: "生成数据库设计方案"
          context:
            enabled: false
            variable_selector: []
          model:
            completion_params:
              temperature: 0.3
              max_tokens: 3000
            mode: chat
            name: Pro/deepseek-ai/DeepSeek-V3
            provider: langgenius/siliconflow/siliconflow
          prompt_template:
            - id: "database-design"
              role: system
              text: |
                基于以下功能清单，生成数据库设计方案：

                功能清单：
                {{function-list.text}}

                请按以下格式输出数据库设计：

                ## 数据库设计

                ### 表：[表名]
                ```sql
                CREATE TABLE `table_name` (
                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                  `field_name` varchar(100) NOT NULL COMMENT '字段描述',
                  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                  PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表描述';
                ```

                请确保表结构规范化，字段类型合理，注释完整清晰。
          selected: false
          title: 数据库设计
          type: llm
          variables: []
          vision:
            enabled: false
        height: 54
        id: "database-design"
        position:
          x: 980
          y: 300
        positionAbsolute:
          x: 980
          y: 300
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244

      # API接口设计节点
      - data:
          desc: "生成API接口文档"
          context:
            enabled: false
            variable_selector: []
          model:
            completion_params:
              temperature: 0.3
              max_tokens: 3000
            mode: chat
            name: Pro/deepseek-ai/DeepSeek-V3
            provider: langgenius/siliconflow/siliconflow
          prompt_template:
            - id: "api-design"
              role: system
              text: |
                基于以下功能清单和数据库设计，生成RESTful API接口文档：

                功能清单：
                {{function-list.text}}

                数据库设计：
                {{database-design.text}}

                请按以下格式输出API接口文档：

                ## API接口文档

                ### 接口：[接口名称]
                - 请求方法：POST/GET/PUT/DELETE
                - 请求路径：/api/v1/resource
                - 功能描述：[接口功能说明]

                **请求参数：**
                ```json
                {
                  "field_name": "string, 必填, 字段描述"
                }
                ```

                **响应示例：**
                ```json
                {
                  "code": 200,
                  "message": "操作成功",
                  "data": {}
                }
                ```

                请确保接口设计符合RESTful规范。
          selected: false
          title: API接口设计
          type: llm
          variables: []
          vision:
            enabled: false
        height: 54
        id: "api-design"
        position:
          x: 1280
          y: 300
        positionAbsolute:
          x: 1280
          y: 300
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244

      # 最终回答节点
      - data:
          answer: |
            ## 🎉 开发文档生成完成！

            您的项目开发文档已经生成完成。

            ### 📋 生成内容包括：

            ## 需求分析
            {{requirements-analysis.text}}

            ## 功能清单
            {{function-list.text}}

            ## 数据库设计
            {{database-design.text}}

            ## API接口文档
            {{api-design.text}}

            ### 💡 后续建议：
            1. 请仔细审查生成的文档内容
            2. 根据实际情况调整和完善细节
            3. 与团队成员讨论确认技术方案
            4. 可以基于此文档开始开发工作

            如需修改或补充任何部分，请告诉我具体需求！
          desc: ""
          selected: false
          title: 最终回答
          type: answer
          variables: []
        height: 54
        id: "final-answer"
        position:
          x: 1580
          y: 300
        positionAbsolute:
          x: 1580
          y: 300
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244

    edges:
      # 主流程连接
      - data:
          isInIteration: false
          sourceType: start
          targetType: llm
        id: "start-requirements-analysis"
        source: "start"
        sourceHandle: source
        target: "requirements-analysis"
        targetHandle: target
        type: custom
        zIndex: 0

      - data:
          isInIteration: false
          sourceType: llm
          targetType: llm
        id: "requirements-analysis-function-list"
        source: "requirements-analysis"
        sourceHandle: source
        target: "function-list"
        targetHandle: target
        type: custom
        zIndex: 0

      - data:
          isInIteration: false
          sourceType: llm
          targetType: llm
        id: "function-list-database-design"
        source: "function-list"
        sourceHandle: source
        target: "database-design"
        targetHandle: target
        type: custom
        zIndex: 0

      - data:
          isInIteration: false
          sourceType: llm
          targetType: llm
        id: "database-design-api-design"
        source: "database-design"
        sourceHandle: source
        target: "api-design"
        targetHandle: target
        type: custom
        zIndex: 0

      - data:
          isInIteration: false
          sourceType: llm
          targetType: answer
        id: "api-design-final-answer"
        source: "api-design"
        sourceHandle: source
        target: "final-answer"
        targetHandle: target
        type: custom
        zIndex: 0

    viewport:
      x: 0
      y: 0
      zoom: 0.8
