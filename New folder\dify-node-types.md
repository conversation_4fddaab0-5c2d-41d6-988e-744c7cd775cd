# Dify工作流节点类型详解

## 1. 开始节点 (start)

开始节点是工作流的入口点，接收用户的初始输入并将其传递给后续节点。

### 基本配置
```yaml
start_node:
  type: start
  title: "开始节点"
  variables: []
  outputs:
    text:
      type: string
  availablePrevNodes: []
  availableNextNodes: []
```

### 关键属性
- `type`: 必须为"start"
- `outputs`: 通常包含一个名为"text"的字符串类型输出，用于传递用户输入

### 注意事项
- 一个工作流中只能有一个开始节点
- 开始节点不能有前置节点，`availablePrevNodes`必须为空数组
- 开始节点通常直接连接到LLM节点或问题分类器节点

## 2. LLM节点 (llm)

LLM节点使用大型语言模型处理文本，是工作流中最常用的节点类型之一。

### 基本配置
```yaml
analyze_text:
  type: llm
  title: "文本分析"
  model:
    provider: langgenius/openai_api_compatible/openai_api_compatible
    name: glm-4-flashx
    mode: chat
  prompt_template:
    - id: system-prompt
      role: system
      text: '{{#context#}}
        你是一个专业的分析师...'
    - id: human-prompt
      role: human
      text: '请分析以下文本：{{text}}'
  variables: []
  outputs:
    text:
      type: string
  context:
    enabled: true
    variable_selector:
      - start_node
      - text
  availablePrevNodes: []
  availableNextNodes: []
```

### 关键属性
- `type`: 必须为"llm"
- `model`: 指定使用的语言模型
  - `provider`: 模型提供者
  - `name`: 模型名称
  - `mode`: 模型模式，通常为"chat"
- `prompt_template`: 提示词模板，包含多个角色的提示
  - `role`: 角色，可以是"system"、"human"或"assistant"
  - `text`: 提示词内容
- `context`: 上下文配置，用于访问其他节点的输出
  - `enabled`: 是否启用上下文
  - `variable_selector`: 选择要访问的变量

### 注意事项
- 使用`{{#context#}}`标记在system提示词开头引入上下文
- 确保使用当前Dify实例中可用的模型
- 提示词中可以使用`{{变量名}}`引用变量

## 3. 代码节点 (code)

代码节点允许执行Python代码进行数据处理和转换。

### 基本配置
```yaml
process_data:
  type: code
  title: "数据处理"
  code_language: python3
  code: |
    import json
    
    def main(input_data) -> dict:
      # 处理逻辑
      result = process_input(input_data)
      return {
        "processed_data": result,
        "status": "success"
      }
      
    def process_input(data):
      # 自定义处理函数
      return data.upper()
  variables: []
  outputs:
    processed_data:
      type: string
    status:
      type: string
  availablePrevNodes: []
  availableNextNodes: []
```

### 关键属性
- `type`: 必须为"code"
- `code_language`: 必须为"python3"
- `code`: Python代码内容
- `outputs`: 定义代码返回的输出变量及其类型

### 注意事项
- 必须使用`code_language: python3`而非`language: python`
- 代码必须包含一个名为`main`的函数作为入口点
- `main`函数必须返回一个字典，其键对应`outputs`中定义的变量名
- 数组类型必须指定元素类型，如`array[object]`、`array[string]`或`array[number]`

## 4. 条件节点 (if-else)

条件节点根据条件评估结果选择不同的执行路径。

### 基本配置
```yaml
check_condition:
  type: if-else
  title: "条件检查"
  variable: customer_type
  operator: equal
  value: "年轻单身型"
  varType: string
  variables: []
  outputs:
    case_id:
      type: string
  availablePrevNodes: []
  availableNextNodes: []
```

### 关键属性
- `type`: 必须为"if-else"
- `variable`: 要评估的变量名
- `operator`: 比较运算符，如"equal"、"not_equal"、"greater_than"等
- `value`: 比较值
- `varType`: 变量类型，如"string"、"number"、"boolean"等

### 注意事项
- `value`的类型必须与`varType`一致
- 当`varType`为"string"时，`value`必须是字符串而不是数组
- 条件节点会创建两个分支：条件为真的分支和条件为假的分支

## 5. 问题分类器节点 (question-classifier)

问题分类器节点根据预定义的规则或模型对输入进行分类。

### 基本配置
```yaml
classify_question:
  type: question-classifier
  title: "问题分类"
  variables: []
  outputs:
    class_name:
      type: string
    class_id:
      type: string
  availablePrevNodes: []
  availableNextNodes: []
```

### 关键属性
- `type`: 必须为"question-classifier"
- `outputs`: 通常包含"class_name"和"class_id"两个输出

### 注意事项
- 确保使用连字符形式的类型名"question-classifier"，而不是下划线形式"question_classifier"
- 问题分类器通常用于工作流的早期阶段，根据用户输入的类型选择不同的处理路径

## 6. 工具节点 (tool)

工具节点用于调用外部API或执行特定功能。

### 基本配置
```yaml
call_api:
  type: tool
  title: "API调用"
  tool_name: "http_request"
  tool_parameters:
    url: "https://api.example.com/data"
    method: "GET"
  variables: []
  outputs:
    response:
      type: object
    status:
      type: string
  availablePrevNodes: []
  availableNextNodes: []
```

### 关键属性
- `type`: 必须为"tool"
- `tool_name`: 工具名称
- `tool_parameters`: 工具参数
- `outputs`: 工具执行结果的输出定义

### 注意事项
- 工具节点的具体配置取决于所使用的工具类型
- 确保工具参数符合工具的要求
- 工具节点通常用于与外部系统集成或执行特定功能

## 7. 知识库节点 (knowledge)

知识库节点用于从知识库中检索相关信息。

### 基本配置
```yaml
query_knowledge:
  type: knowledge
  title: "知识库查询"
  knowledge_id: "your_knowledge_base_id"
  top_k: 3
  score_threshold: 0.7
  rerank: true
  variables: []
  outputs:
    documents:
      type: array[object]
    query:
      type: string
  availablePrevNodes: []
  availableNextNodes: []
```

### 关键属性
- `type`: 必须为"knowledge"
- `knowledge_id`: 知识库ID
- `top_k`: 返回的最大结果数
- `score_threshold`: 相似度阈值
- `rerank`: 是否重新排序结果

### 注意事项
- 知识库节点需要预先创建并配置知识库
- 输出通常包含检索到的文档和原始查询

## 8. 数据集成节点 (data-integration)

数据集成节点用于连接和处理来自不同数据源的数据。

### 基本配置
```yaml
integrate_data:
  type: data-integration
  title: "数据集成"
  integration_type: "database"
  connection_id: "your_connection_id"
  query: "SELECT * FROM users WHERE id = {{user_id}}"
  variables: []
  outputs:
    results:
      type: array[object]
    count:
      type: number
  availablePrevNodes: []
  availableNextNodes: []
```

### 关键属性
- `type`: 必须为"data-integration"
- `integration_type`: 集成类型
- `connection_id`: 连接ID
- `query`: 查询语句

### 注意事项
- 数据集成节点需要预先配置数据源连接
- 查询语句可以包含变量引用
