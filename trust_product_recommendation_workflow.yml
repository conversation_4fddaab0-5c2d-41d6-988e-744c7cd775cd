kind: app
version: "0.1.5"
app:
  mode: workflow
  name: 个性化信托产品推荐系统
  description: "根据客户画像，分析客户需求与投资偏好，提供定制的信托产品解决方案"
  icon: "💰"
  icon_background: "#E6F7FF"
  use_icon_as_answer_icon: false

workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
    opening_statement: ''
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
      - source: start_node
        target: data_preprocessing
        id: start_node-source-data_preprocessing-target
        data:
          sourceType: start
          targetType: code
          isInIteration: false
        sourceHandle: source
        targetHandle: target
        type: custom
        zIndex: 0
      - source: data_preprocessing
        target: risk_assessment
        id: data_preprocessing-source-risk_assessment-target
        data:
          sourceType: code
          targetType: code
          isInIteration: false
        sourceHandle: source
        targetHandle: target
        type: custom
        zIndex: 0
      - source: risk_assessment
        target: product_query
        id: risk_assessment-source-product_query-target
        data:
          sourceType: code
          targetType: code
          isInIteration: false
        sourceHandle: source
        targetHandle: target
        type: custom
        zIndex: 0
      - source: product_query
        target: risk_branch
        id: product_query-source-risk_branch-target
        data:
          sourceType: code
          targetType: if-else
          isInIteration: false
        sourceHandle: source
        targetHandle: target
        type: custom
        zIndex: 0
      - source: risk_branch
        target: conservative_filter
        id: risk_branch-source-conservative_filter-target
        data:
          sourceType: if-else
          targetType: code
          isInIteration: false
        sourceHandle: '1'
        targetHandle: target
        type: custom
        zIndex: 0
      - source: risk_branch
        target: aggressive_filter
        id: risk_branch-source-aggressive_filter-target
        data:
          sourceType: if-else
          targetType: code
          isInIteration: false
        sourceHandle: '2'
        targetHandle: target
        type: custom
        zIndex: 0
      - source: conservative_filter
        target: product_matching
        id: conservative_filter-source-product_matching-target
        data:
          sourceType: code
          targetType: code
          isInIteration: false
        sourceHandle: source
        targetHandle: target
        type: custom
        zIndex: 0
      - source: aggressive_filter
        target: product_matching
        id: aggressive_filter-source-product_matching-target
        data:
          sourceType: code
          targetType: code
          isInIteration: false
        sourceHandle: source
        targetHandle: target
        type: custom
        zIndex: 0
      - source: product_matching
        target: generate_recommendations
        id: product_matching-source-generate_recommendations-target
        data:
          sourceType: code
          targetType: llm
          isInIteration: false
        sourceHandle: source
        targetHandle: target
        type: custom
        zIndex: 0
      - source: generate_recommendations
        target: result_sorting
        id: generate_recommendations-source-result_sorting-target
        data:
          sourceType: llm
          targetType: code
          isInIteration: false
        sourceHandle: source
        targetHandle: target
        type: custom
        zIndex: 0
      - source: result_sorting
        target: generate_report
        id: result_sorting-source-generate_report-target
        data:
          sourceType: code
          targetType: llm
          isInIteration: false
        sourceHandle: source
        targetHandle: target
        type: custom
        zIndex: 0
      - source: generate_report
        target: end_node
        id: generate_report-source-end_node-target
        data:
          sourceType: llm
          targetType: end
          isInIteration: false
        sourceHandle: source
        targetHandle: target
        type: custom
        zIndex: 0

    nodes:
      - data:
          type: start
          title: 收集客户信息
          variables:
            - type: paragraph
              variable: age
              label: 年龄
              required: true
              max_length: 10
            - type: paragraph
              variable: gender
              label: 性别
              required: true
              max_length: 10
            - type: paragraph
              variable: income
              label: 月收入(元)
              required: true
              max_length: 20
            - type: paragraph
              variable: assets
              label: 总资产(元)
              required: true
              max_length: 50
            - type: paragraph
              variable: investment_experience
              label: 投资经验
              required: true
              max_length: 100
            - type: paragraph
              variable: investment_goal
              label: 投资目标
              required: true
              max_length: 200
            - type: paragraph
              variable: investment_horizon
              label: 投资期限
              required: true
              max_length: 50
            - type: paragraph
              variable: risk_tolerance
              label: 风险承受能力
              required: true
              max_length: 50
            - type: paragraph
              variable: liquidity_needs
              label: 流动性需求
              required: true
              max_length: 100
            - type: paragraph
              variable: tax_considerations
              label: 税务考量
              required: false
              max_length: 200
            - type: paragraph
              variable: special_requirements
              label: 特殊需求
              required: false
              max_length: 200
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: start_node

      - data:
          type: code
          title: 数据清洗预处理
          code_language: python3
          variables:
            - value_selector:
                - start_node
                - age
                - gender
                - income
                - assets
                - investment_experience
                - investment_goal
                - investment_horizon
                - risk_tolerance
                - liquidity_needs
                - tax_considerations
                - special_requirements
              variable: input_data
          outputs:
            processed_data:
              type: object
          code: |
            import json
            import re

            def main(input_data) -> dict:
                # 初始化处理后的数据字典
                processed_data = {
                    "age": 0,
                    "gender": "",
                    "income": 0,
                    "assets": 0,
                    "investment_experience": "",
                    "investment_goal": "",
                    "investment_horizon": 0,
                    "risk_tolerance": "",
                    "liquidity_needs": "",
                    "tax_considerations": "",
                    "special_requirements": ""
                }

                # 处理年龄
                try:
                    age_str = input_data.age
                    # 提取数字
                    age_match = re.search(r'\d+', age_str)
                    if age_match:
                        processed_data["age"] = int(age_match.group())
                except:
                    processed_data["age"] = 35  # 默认值

                # 处理性别
                try:
                    gender = input_data.gender.strip()
                    if "男" in gender:
                        processed_data["gender"] = "male"
                    elif "女" in gender:
                        processed_data["gender"] = "female"
                    else:
                        processed_data["gender"] = gender
                except:
                    processed_data["gender"] = "unknown"

                # 处理月收入
                try:
                    income_str = input_data.income
                    # 提取数字
                    income_match = re.search(r'\d+(?:\.\d+)?', income_str)
                    if income_match:
                        income_value = float(income_match.group())
                        # 如果包含“万”，则乘以10000
                        if "万" in income_str:
                            income_value *= 10000
                        processed_data["income"] = income_value
                except:
                    processed_data["income"] = 20000  # 默认值

                # 处理总资产
                try:
                    assets_str = input_data.assets
                    # 提取数字
                    assets_match = re.search(r'\d+(?:\.\d+)?', assets_str)
                    if assets_match:
                        assets_value = float(assets_match.group())
                        # 如果包含“万”，则乘以10000
                        if "万" in assets_str:
                            assets_value *= 10000
                        # 如果包含“亿”，则乘以100000000
                        elif "亿" in assets_str:
                            assets_value *= 100000000
                        processed_data["assets"] = assets_value
                except:
                    processed_data["assets"] = 1000000  # 默认值

                # 处理投资经验
                try:
                    exp = input_data.investment_experience.strip().lower()
                    if any(keyword in exp for keyword in ["无经验", "新手", "初学者"]):
                        processed_data["investment_experience"] = "beginner"
                    elif any(keyword in exp for keyword in ["有经验", "中级", "一般"]):
                        processed_data["investment_experience"] = "intermediate"
                    elif any(keyword in exp for keyword in ["丰富", "高级", "专业"]):
                        processed_data["investment_experience"] = "advanced"
                    else:
                        processed_data["investment_experience"] = exp
                except:
                    processed_data["investment_experience"] = "beginner"

                # 处理投资目标
                try:
                    goal = input_data.investment_goal.strip()
                    processed_data["investment_goal"] = goal
                except:
                    processed_data["investment_goal"] = "资产增值"

                # 处理投资期限
                try:
                    horizon_str = input_data.investment_horizon
                    # 提取数字
                    horizon_match = re.search(r'\d+', horizon_str)
                    if horizon_match:
                        horizon_value = int(horizon_match.group())
                        # 如果单位是月，转换为年
                        if "月" in horizon_str:
                            horizon_value = max(1, round(horizon_value / 12))
                        processed_data["investment_horizon"] = horizon_value
                    else:
                        # 根据关键词判断
                        if "短期" in horizon_str:
                            processed_data["investment_horizon"] = 1
                        elif "中期" in horizon_str:
                            processed_data["investment_horizon"] = 3
                        elif "长期" in horizon_str:
                            processed_data["investment_horizon"] = 5
                        else:
                            processed_data["investment_horizon"] = 2
                except:
                    processed_data["investment_horizon"] = 2  # 默认值

                # 处理风险承受能力
                try:
                    risk = input_data.risk_tolerance.strip().lower()
                    if any(keyword in risk for keyword in ["保守", "低", "安全"]):
                        processed_data["risk_tolerance"] = "low"
                    elif any(keyword in risk for keyword in ["中等", "平衡", "适中"]):
                        processed_data["risk_tolerance"] = "medium"
                    elif any(keyword in risk for keyword in ["激进", "高", "冠军"]):
                        processed_data["risk_tolerance"] = "high"
                    else:
                        processed_data["risk_tolerance"] = risk
                except:
                    processed_data["risk_tolerance"] = "medium"

                # 处理流动性需求
                try:
                    liquidity = input_data.liquidity_needs.strip().lower()
                    if any(keyword in liquidity for keyword in ["低", "不需要", "小"]):
                        processed_data["liquidity_needs"] = "low"
                    elif any(keyword in liquidity for keyword in ["中", "一般", "适中"]):
                        processed_data["liquidity_needs"] = "medium"
                    elif any(keyword in liquidity for keyword in ["高", "强", "大"]):
                        processed_data["liquidity_needs"] = "high"
                    else:
                        processed_data["liquidity_needs"] = liquidity
                except:
                    processed_data["liquidity_needs"] = "medium"

                # 处理税务考量
                try:
                    tax = input_data.tax_considerations
                    processed_data["tax_considerations"] = tax if tax else "无特殊税务考量"
                except:
                    processed_data["tax_considerations"] = "无特殊税务考量"

                # 处理特殊需求
                try:
                    special = input_data.special_requirements
                    processed_data["special_requirements"] = special if special else "无特殊需求"
                except:
                    processed_data["special_requirements"] = "无特殊需求"

                return {"processed_data": processed_data}
          context:
            enabled: true
            variable_selector:
              - start_node
              - age
              - gender
              - income
              - assets
              - investment_experience
              - investment_goal
              - investment_horizon
              - risk_tolerance
              - liquidity_needs
              - tax_considerations
              - special_requirements
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: data_preprocessing

      - data:
          type: code
          title: 风险等级评估
          code_language: python3
          variables:
            - value_selector:
                - data_preprocessing
                - processed_data
              variable: input_data
          outputs:
            risk_score:
              type: number
            risk_level:
              type: string
            risk_profile:
              type: object
          code: |
            def main(input_data) -> dict:
                # 获取处理后的客户数据
                data = input_data

                # 初始化风险评分
                risk_score = 0

                # 1. 年龄因素 (越年轻风险承受能力越强)
                age = data.get("age", 35)
                if age < 30:
                    risk_score += 20
                elif age < 40:
                    risk_score += 15
                elif age < 50:
                    risk_score += 10
                elif age < 60:
                    risk_score += 5
                else:
                    risk_score += 0

                # 2. 收入因素 (收入越高风险承受能力越强)
                income = data.get("income", 20000)
                if income > 100000:
                    risk_score += 20
                elif income > 50000:
                    risk_score += 15
                elif income > 20000:
                    risk_score += 10
                elif income > 10000:
                    risk_score += 5
                else:
                    risk_score += 0

                # 3. 总资产因素 (资产越多风险承受能力越强)
                assets = data.get("assets", 1000000)
                if assets > 10000000:  # 1000万
                    risk_score += 20
                elif assets > 5000000:  # 500万
                    risk_score += 15
                elif assets > 1000000:  # 100万
                    risk_score += 10
                elif assets > 500000:  # 50万
                    risk_score += 5
                else:
                    risk_score += 0

                # 4. 投资经验因素 (经验越丰富风险承受能力越强)
                experience = data.get("investment_experience", "beginner")
                if experience == "advanced":
                    risk_score += 20
                elif experience == "intermediate":
                    risk_score += 10
                else:  # beginner
                    risk_score += 0

                # 5. 投资期限因素 (期限越长风险承受能力越强)
                horizon = data.get("investment_horizon", 2)
                if horizon >= 5:
                    risk_score += 20
                elif horizon >= 3:
                    risk_score += 10
                else:
                    risk_score += 0

                # 6. 风险偏好因素 (直接影响)
                risk_tolerance = data.get("risk_tolerance", "medium")
                if risk_tolerance == "high":
                    risk_score += 20
                elif risk_tolerance == "medium":
                    risk_score += 10
                else:  # low
                    risk_score += 0

                # 7. 流动性需求因素 (需求越高风险承受能力越低)
                liquidity_needs = data.get("liquidity_needs", "medium")
                if liquidity_needs == "low":
                    risk_score += 10
                elif liquidity_needs == "medium":
                    risk_score += 5
                else:  # high
                    risk_score += 0

                # 根据总分确定风险等级 (R1-R5)
                # 总分范围: 0-110
                if risk_score >= 90:
                    risk_level = "R5"  # 激进型
                elif risk_score >= 70:
                    risk_level = "R4"  # 进取型
                elif risk_score >= 50:
                    risk_level = "R3"  # 平衡型
                elif risk_score >= 30:
                    risk_level = "R2"  # 稳健型
                else:
                    risk_level = "R1"  # 保守型

                # 创建风险画像
                risk_profile = {
                    "risk_score": risk_score,
                    "risk_level": risk_level,
                    "age_factor": age,
                    "income_factor": income,
                    "assets_factor": assets,
                    "experience_factor": experience,
                    "horizon_factor": horizon,
                    "risk_tolerance_factor": risk_tolerance,
                    "liquidity_needs_factor": liquidity_needs,
                    "suitable_product_types": get_suitable_product_types(risk_level)
                }

                return {
                    "risk_score": risk_score,
                    "risk_level": risk_level,
                    "risk_profile": risk_profile
                }

            def get_suitable_product_types(risk_level):
                """Based on risk level, return suitable product types"""
                if risk_level == "R1":
                    return ["cash_management", "fixed_income", "capital_protected"]
                elif risk_level == "R2":
                    return ["fixed_income", "capital_protected", "low_risk_mixed"]
                elif risk_level == "R3":
                    return ["low_risk_mixed", "balanced_mixed", "bond_enhanced"]
                elif risk_level == "R4":
                    return ["balanced_mixed", "equity_enhanced", "alternative_investment"]
                elif risk_level == "R5":
                    return ["equity_enhanced", "alternative_investment", "high_yield"]
                else:
                    return ["fixed_income", "capital_protected"]
          context:
            enabled: true
            variable_selector:
              - data_preprocessing
              - processed_data
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: risk_assessment

      - data:
          type: code
          title: 产品库查询
          code_language: python3
          variables:
            - value_selector:
                - risk_assessment
                - risk_level
              variable: risk_level
          outputs:
            products:
              type: array[object]
          code: |
            def main(risk_level) -> dict:
                # 模拟产品库查询
                # 在实际应用中，这里应该调用产品数据库API

                # 创建模拟产品列表
                all_products = [
                    # 保守型产品 (R1-R2)
                    {
                        "id": "P001",
                        "name": "安心现金管理信托",
                        "type": "cash_management",
                        "risk_level": "R1",
                        "expected_return": "4.0-4.5%",
                        "min_investment": 50000,
                        "term": "3个月",
                        "term_months": 3,
                        "liquidity": "high",
                        "features": "低风险、高流动性、稳定收益",
                        "suitable_for": "资金安全性需求高的保守型投资者",
                        "description": "主要投资于货币市场工具和短期债券，提供稳定的现金管理服务。"
                    },
                    {
                        "id": "P002",
                        "name": "恒安固收益信托",
                        "type": "fixed_income",
                        "risk_level": "R1",
                        "expected_return": "4.5-5.0%",
                        "min_investment": 100000,
                        "term": "6个月",
                        "term_months": 6,
                        "liquidity": "medium",
                        "features": "低风险、固定收益、期限灵活",
                        "suitable_for": "追求稳定收益的保守型投资者",
                        "description": "主要投资于高质量债券和债权类资产，提供稳定的固定收益。"
                    },
                    {
                        "id": "P003",
                        "name": "安盈保本增值信托",
                        "type": "capital_protected",
                        "risk_level": "R2",
                        "expected_return": "5.0-5.5%",
                        "min_investment": 200000,
                        "term": "12个月",
                        "term_months": 12,
                        "liquidity": "low",
                        "features": "本金保障、适度增值、稳健型",
                        "suitable_for": "追求本金安全的稳健型投资者",
                        "description": "采用结构化设计，确保本金安全的同时提供适度的投资收益。"
                    },
                    {
                        "id": "P004",
                        "name": "安盛低风险混合信托",
                        "type": "low_risk_mixed",
                        "risk_level": "R2",
                        "expected_return": "5.5-6.0%",
                        "min_investment": 300000,
                        "term": "18个月",
                        "term_months": 18,
                        "liquidity": "low",
                        "features": "低波动、稳健增值、多元配置",
                        "suitable_for": "追求稳健增值的保守型投资者",
                        "description": "主要配置债券为主，少量配置优质股票，实现低风险的稳健增值。"
                    },

                    # 平衡型产品 (R3)
                    {
                        "id": "P005",
                        "name": "平衡增值混合信托",
                        "type": "balanced_mixed",
                        "risk_level": "R3",
                        "expected_return": "6.0-7.0%",
                        "min_investment": 500000,
                        "term": "24个月",
                        "term_months": 24,
                        "liquidity": "medium",
                        "features": "平衡配置、中等风险、增值潜力",
                        "suitable_for": "追求平衡风险收益的投资者",
                        "description": "平衡配置股票、债券和可转债，实现风险与收益的平衡。"
                    },
                    {
                        "id": "P006",
                        "name": "债券增强型信托",
                        "type": "bond_enhanced",
                        "risk_level": "R3",
                        "expected_return": "6.5-7.5%",
                        "min_investment": 500000,
                        "term": "24个月",
                        "term_months": 24,
                        "liquidity": "medium",
                        "features": "债券为主、稳定增强、中等风险",
                        "suitable_for": "追求债券增强型收益的投资者",
                        "description": "主要投资于各类债券和债权类资产，通过主动管理提升收益。"
                    },

                    # 进取型产品 (R4-R5)
                    {
                        "id": "P007",
                        "name": "股票增强型信托",
                        "type": "equity_enhanced",
                        "risk_level": "R4",
                        "expected_return": "8.0-10.0%",
                        "min_investment": 1000000,
                        "term": "36个月",
                        "term_months": 36,
                        "liquidity": "low",
                        "features": "股票为主、进取增值、较高风险",
                        "suitable_for": "追求较高收益的进取型投资者",
                        "description": "主要投资于优质股票和股权类资产，追求资本增值。"
                    },
                    {
                        "id": "P008",
                        "name": "另类资产投资信托",
                        "type": "alternative_investment",
                        "risk_level": "R4",
                        "expected_return": "9.0-12.0%",
                        "min_investment": 2000000,
                        "term": "48个月",
                        "term_months": 48,
                        "liquidity": "very_low",
                        "features": "另类资产、低相关性、高潜力收益",
                        "suitable_for": "追求组合分散的进取型投资者",
                        "description": "投资于私募股权、地产、商品等另类资产，提供组合分散效应。"
                    },
                    {
                        "id": "P009",
                        "name": "高收益机会信托",
                        "type": "high_yield",
                        "risk_level": "R5",
                        "expected_return": "12.0-15.0%",
                        "min_investment": 3000000,
                        "term": "60个月",
                        "term_months": 60,
                        "liquidity": "very_low",
                        "features": "高风险、高收益、激进型",
                        "suitable_for": "追求高收益的激进型投资者",
                        "description": "投资于高收益债券、新兴市场股票和特殊机会等高风险资产。"
                    },
                    {
                        "id": "P010",
                        "name": "创新私募信托",
                        "type": "high_yield",
                        "risk_level": "R5",
                        "expected_return": "15.0-20.0%",
                        "min_investment": 5000000,
                        "term": "60个月",
                        "term_months": 60,
                        "liquidity": "very_low",
                        "features": "极高风险、极高收益、创新型",
                        "suitable_for": "追求极高收益的激进型投资者",
                        "description": "投资于初创企业、私募股权和创新项目，追求极高收益。"
                    }
                ]

                # 返回所有产品，在后续节点中进行筛选
                return {"products": all_products}
          context:
            enabled: true
            variable_selector:
              - risk_assessment
              - risk_level
              - risk_score
              - risk_profile
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: product_query

      - data:
          type: if-else
          title: 风险等级分流
          cases:
            - case_id: '1'
              conditions:
                - comparison_operator: in
                  id: condition1
                  value: ["R1", "R2"]
                  varType: string
                  variable_selector:
                    - risk_assessment
                    - risk_level
              id: '1'
              logical_operator: and
            - case_id: '2'
              conditions:
                - comparison_operator: in
                  id: condition2
                  value: ["R3", "R4", "R5"]
                  varType: string
                  variable_selector:
                    - risk_assessment
                    - risk_level
              id: '2'
              logical_operator: and
          outputs:
            case_id:
              type: string
          context:
            enabled: true
            variable_selector:
              - risk_assessment
              - risk_level
              - risk_score
              - risk_profile
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: risk_branch

      - data:
          type: code
          title: 保守型产品筛选
          code_language: python3
          variables:
            - value_selector:
                - product_query
                - products
              variable: all_products
            - value_selector:
                - risk_assessment
                - risk_profile
              variable: risk_profile
            - value_selector:
                - data_preprocessing
                - processed_data
              variable: client_data
          outputs:
            filtered_products:
              type: array[object]
          code: |
            def main(all_products, risk_profile, client_data) -> dict:
                # 筛选保守型产品 (R1-R2)
                conservative_products = [p for p in all_products if p["risk_level"] in ["R1", "R2"]]

                # 如果没有保守型产品，返回空列表
                if not conservative_products:
                    return {"filtered_products": []}

                # 根据客户数据进行进一步筛选
                filtered_products = []

                # 获取客户数据
                investment_amount = client_data.get("assets", 0) * 0.3  # 默认可投资金额为总资产的30%
                investment_horizon = client_data.get("investment_horizon", 2)  # 默认投资期阵2年
                liquidity_needs = client_data.get("liquidity_needs", "medium")  # 默认流动性需求中等

                # 根据投资金额筛选
                affordable_products = [p for p in conservative_products if p["min_investment"] <= investment_amount]

                # 根据投资期限筛选
                horizon_matched_products = []
                for product in affordable_products:
                    # 将产品期限转换为年
                    product_horizon_years = product["term_months"] / 12
                    # 如果产品期限小于等于客户投资期限，或者客户期限足够长，则匹配
                    if product_horizon_years <= investment_horizon or investment_horizon >= 5:
                        horizon_matched_products.append(product)

                # 根据流动性需求筛选
                if liquidity_needs == "high":
                    # 高流动性需求，优先选择流动性高的产品
                    liquidity_matched_products = [p for p in horizon_matched_products if p["liquidity"] in ["high", "medium"]]
                    if not liquidity_matched_products and horizon_matched_products:  # 如果没有高流动性产品，使用原列表
                        liquidity_matched_products = horizon_matched_products
                elif liquidity_needs == "medium":
                    # 中等流动性需求，不特别筛选
                    liquidity_matched_products = horizon_matched_products
                else:  # low
                    # 低流动性需求，可以接受所有产品
                    liquidity_matched_products = horizon_matched_products

                # 最终筛选结果
                filtered_products = liquidity_matched_products

                # 添加匹配度分数
                for product in filtered_products:
                    # 计算匹配度分数
                    match_score = calculate_match_score(product, client_data, risk_profile)
                    product["match_score"] = match_score

                return {"filtered_products": filtered_products}

            def calculate_match_score(product, client_data, risk_profile):
                """Calculate how well the product matches the client's needs"""
                score = 70  # 基础分

                # 1. 风险匹配度 (0-30分)
                client_risk_level = risk_profile.get("risk_level", "R3")
                product_risk_level = product.get("risk_level", "R3")

                risk_levels = ["R1", "R2", "R3", "R4", "R5"]
                client_risk_index = risk_levels.index(client_risk_level)
                product_risk_index = risk_levels.index(product_risk_level)

                # 风险差距，越小越好
                risk_distance = abs(client_risk_index - product_risk_index)
                if risk_distance == 0:
                    score += 30  # 完全匹配
                elif risk_distance == 1:
                    score += 20  # 相差一级
                elif risk_distance == 2:
                    score += 10  # 相差两级

                # 2. 投资期限匹配度 (0-20分)
                client_horizon = client_data.get("investment_horizon", 2)  # 年
                product_horizon = product.get("term_months", 12) / 12  # 转换为年

                horizon_ratio = product_horizon / client_horizon if client_horizon > 0 else 1
                if 0.8 <= horizon_ratio <= 1.2:  # 在客户期限的正负20%范围内
                    score += 20
                elif 0.5 <= horizon_ratio <= 1.5:  # 在客户期限的正负50%范围内
                    score += 10
                elif product_horizon < client_horizon:  # 产品期限短于客户期限
                    score += 5

                # 3. 流动性匹配度 (0-20分)
                client_liquidity = client_data.get("liquidity_needs", "medium")
                product_liquidity = product.get("liquidity", "medium")

                liquidity_levels = {"high": 3, "medium": 2, "low": 1, "very_low": 0}
                client_liquidity_value = liquidity_levels.get(client_liquidity, 2)
                product_liquidity_value = liquidity_levels.get(product_liquidity, 2)

                if client_liquidity_value == product_liquidity_value:
                    score += 20  # 完全匹配
                elif client_liquidity_value > product_liquidity_value:
                    # 客户需要更高的流动性，但产品流动性低
                    score += 5
                else:
                    # 客户流动性需求低，产品流动性高
                    score += 15

                # 4. 投资金额匹配度 (0-10分)
                client_assets = client_data.get("assets", 1000000)
                investment_capacity = client_assets * 0.3  # 假设可投资金额为总资产的30%
                product_min_investment = product.get("min_investment", 100000)

                investment_ratio = product_min_investment / investment_capacity if investment_capacity > 0 else 1
                if investment_ratio <= 0.3:  # 产品最低投资金额不超过可投资金额的30%
                    score += 10
                elif investment_ratio <= 0.5:  # 产品最低投资金额不超过可投资金额的50%
                    score += 5

                # 5. 投资目标匹配度 (0-20分)
                # 这里可以根据客户的投资目标和产品特点进行匹配
                # 简化处理，根据产品类型和客户目标关键词匹配
                client_goal = client_data.get("investment_goal", "").lower()
                product_type = product.get("type", "")
                product_features = product.get("features", "").lower()

                goal_keywords = {
                    "cash_management": ["流动性", "现金", "安全"],
                    "fixed_income": ["收益", "稳定", "利息"],
                    "capital_protected": ["保本", "安全", "保障"],
                    "low_risk_mixed": ["稳健", "低风险", "增值"],
                    "balanced_mixed": ["平衡", "增值", "稳定增长"],
                    "bond_enhanced": ["债券", "增强", "收益"],
                    "equity_enhanced": ["股票", "增值", "增长"],
                    "alternative_investment": ["另类", "多元", "分散"],
                    "high_yield": ["高收益", "激进", "机会"]
                }

                # 检查客户目标中是否包含产品类型相关的关键词
                if product_type in goal_keywords:
                    for keyword in goal_keywords[product_type]:
                        if keyword in client_goal:
                            score += 10
                            break

                # 检查产品特点中是否包含客户目标相关的关键词
                goal_words = client_goal.split()
                for word in goal_words:
                    if len(word) >= 2 and word in product_features:  # 只检查长度至少2的词
                        score += 10
                        break

                # 确保分数在合理范围内
                return min(100, max(0, score))
          context:
            enabled: true
            variable_selector:
              - product_query
              - products
              - risk_assessment
              - risk_profile
              - data_preprocessing
              - processed_data
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: conservative_filter

      - data:
          type: code
          title: 进取型产品筛选
          code_language: python3
          variables:
            - value_selector:
                - product_query
                - products
              variable: all_products
            - value_selector:
                - risk_assessment
                - risk_profile
              variable: risk_profile
            - value_selector:
                - data_preprocessing
                - processed_data
              variable: client_data
          outputs:
            filtered_products:
              type: array[object]
          code: |
            def main(all_products, risk_profile, client_data) -> dict:
                # 筛选进取型产品 (R3-R5)
                aggressive_products = [p for p in all_products if p["risk_level"] in ["R3", "R4", "R5"]]

                # 如果没有进取型产品，返回空列表
                if not aggressive_products:
                    return {"filtered_products": []}

                # 根据客户数据进行进一步筛选
                filtered_products = []

                # 获取客户数据
                investment_amount = client_data.get("assets", 0) * 0.3  # 默认可投资金额为总资产的30%
                investment_horizon = client_data.get("investment_horizon", 2)  # 默认投资期阵2年
                liquidity_needs = client_data.get("liquidity_needs", "medium")  # 默认流动性需求中等
                client_risk_level = risk_profile.get("risk_level", "R3")

                # 根据风险等级进行筛选
                risk_levels = ["R1", "R2", "R3", "R4", "R5"]
                client_risk_index = risk_levels.index(client_risk_level)

                # 对于进取型产品，确保产品风险等级不超过客户风险等级太多
                risk_filtered_products = []
                for product in aggressive_products:
                    product_risk_level = product.get("risk_level", "R3")
                    product_risk_index = risk_levels.index(product_risk_level)

                    # 如果客户风险等级为R3，只允许R3及以下产品
                    # 如果客户风险等级为R4，允许R3-R4产品
                    # 如果客户风险等级为R5，允许所有产品
                    if product_risk_index <= client_risk_index:
                        risk_filtered_products.append(product)

                # 根据投资金额筛选
                affordable_products = [p for p in risk_filtered_products if p["min_investment"] <= investment_amount]

                # 根据投资期限筛选
                horizon_matched_products = []
                for product in affordable_products:
                    # 将产品期限转换为年
                    product_horizon_years = product["term_months"] / 12

                    # 对于进取型产品，客户期限应该大于等于产品期限
                    # 或者客户期限足够长（大于等于5年）
                    if investment_horizon >= product_horizon_years or investment_horizon >= 5:
                        horizon_matched_products.append(product)

                # 根据流动性需求筛选
                # 对于进取型产品，流动性通常较低，因此对流动性高的需求要更严格
                if liquidity_needs == "high":
                    # 高流动性需求，只选择流动性高的产品
                    liquidity_matched_products = [p for p in horizon_matched_products if p["liquidity"] == "high"]
                    if not liquidity_matched_products:  # 如果没有高流动性产品，选择中等流动性产品
                        liquidity_matched_products = [p for p in horizon_matched_products if p["liquidity"] == "medium"]
                    if not liquidity_matched_products and horizon_matched_products:  # 如果仍然没有，使用原列表
                        liquidity_matched_products = horizon_matched_products
                elif liquidity_needs == "medium":
                    # 中等流动性需求，选择中等或高流动性产品
                    liquidity_matched_products = [p for p in horizon_matched_products if p["liquidity"] in ["high", "medium"]]
                    if not liquidity_matched_products and horizon_matched_products:  # 如果没有，使用原列表
                        liquidity_matched_products = horizon_matched_products
                else:  # low
                    # 低流动性需求，可以接受所有产品
                    liquidity_matched_products = horizon_matched_products

                # 最终筛选结果
                filtered_products = liquidity_matched_products

                # 添加匹配度分数
                for product in filtered_products:
                    # 计算匹配度分数
                    match_score = calculate_match_score(product, client_data, risk_profile)
                    product["match_score"] = match_score

                return {"filtered_products": filtered_products}

            def calculate_match_score(product, client_data, risk_profile):
                """Calculate how well the product matches the client's needs"""
                score = 70  # 基础分

                # 1. 风险匹配度 (0-30分)
                client_risk_level = risk_profile.get("risk_level", "R3")
                product_risk_level = product.get("risk_level", "R3")

                risk_levels = ["R1", "R2", "R3", "R4", "R5"]
                client_risk_index = risk_levels.index(client_risk_level)
                product_risk_index = risk_levels.index(product_risk_level)

                # 风险差距，越小越好
                risk_distance = abs(client_risk_index - product_risk_index)
                if risk_distance == 0:
                    score += 30  # 完全匹配
                elif risk_distance == 1:
                    score += 20  # 相差一级
                elif risk_distance == 2:
                    score += 10  # 相差两级

                # 2. 投资期限匹配度 (0-20分)
                client_horizon = client_data.get("investment_horizon", 2)  # 年
                product_horizon = product.get("term_months", 12) / 12  # 转换为年

                horizon_ratio = product_horizon / client_horizon if client_horizon > 0 else 1
                if 0.8 <= horizon_ratio <= 1.2:  # 在客户期限的正负20%范围内
                    score += 20
                elif 0.5 <= horizon_ratio <= 1.5:  # 在客户期限的正负50%范围内
                    score += 10
                elif product_horizon < client_horizon:  # 产品期限短于客户期限
                    score += 5

                # 3. 流动性匹配度 (0-20分)
                client_liquidity = client_data.get("liquidity_needs", "medium")
                product_liquidity = product.get("liquidity", "medium")

                liquidity_levels = {"high": 3, "medium": 2, "low": 1, "very_low": 0}
                client_liquidity_value = liquidity_levels.get(client_liquidity, 2)
                product_liquidity_value = liquidity_levels.get(product_liquidity, 2)

                if client_liquidity_value == product_liquidity_value:
                    score += 20  # 完全匹配
                elif client_liquidity_value > product_liquidity_value:
                    # 客户需要更高的流动性，但产品流动性低
                    score += 5
                else:
                    # 客户流动性需求低，产品流动性高
                    score += 15

                # 4. 投资金额匹配度 (0-10分)
                client_assets = client_data.get("assets", 1000000)
                investment_capacity = client_assets * 0.3  # 假设可投资金额为总资产的30%
                product_min_investment = product.get("min_investment", 100000)

                investment_ratio = product_min_investment / investment_capacity if investment_capacity > 0 else 1
                if investment_ratio <= 0.3:  # 产品最低投资金额不超过可投资金额的30%
                    score += 10
                elif investment_ratio <= 0.5:  # 产品最低投资金额不超过可投资金额的50%
                    score += 5

                # 5. 投资目标匹配度 (0-20分)
                # 这里可以根据客户的投资目标和产品特点进行匹配
                # 简化处理，根据产品类型和客户目标关键词匹配
                client_goal = client_data.get("investment_goal", "").lower()
                product_type = product.get("type", "")
                product_features = product.get("features", "").lower()

                goal_keywords = {
                    "cash_management": ["流动性", "现金", "安全"],
                    "fixed_income": ["收益", "稳定", "利息"],
                    "capital_protected": ["保本", "安全", "保障"],
                    "low_risk_mixed": ["稳健", "低风险", "增值"],
                    "balanced_mixed": ["平衡", "增值", "稳定增长"],
                    "bond_enhanced": ["债券", "增强", "收益"],
                    "equity_enhanced": ["股票", "增值", "增长"],
                    "alternative_investment": ["另类", "多元", "分散"],
                    "high_yield": ["高收益", "激进", "机会"]
                }

                # 检查客户目标中是否包含产品类型相关的关键词
                if product_type in goal_keywords:
                    for keyword in goal_keywords[product_type]:
                        if keyword in client_goal:
                            score += 10
                            break

                # 检查产品特点中是否包含客户目标相关的关键词
                goal_words = client_goal.split()
                for word in goal_words:
                    if len(word) >= 2 and word in product_features:  # 只检查长度至少2的词
                        score += 10
                        break

                # 确保分数在合理范围内
                return min(100, max(0, score))
          context:
            enabled: true
            variable_selector:
              - product_query
              - products
              - risk_assessment
              - risk_profile
              - data_preprocessing
              - processed_data
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: aggressive_filter

      - data:
          type: code
          title: 产品匹配算法
          code_language: python3
          variables:
            - value_selector:
                - conservative_filter
                - filtered_products
              variable: conservative_products
            - value_selector:
                - aggressive_filter
                - filtered_products
              variable: aggressive_products
            - value_selector:
                - data_preprocessing
                - processed_data
              variable: client_data
            - value_selector:
                - risk_assessment
                - risk_profile
              variable: risk_profile
          outputs:
            matched_products:
              type: array[object]
            portfolio_recommendation:
              type: object
          code: |
            def main(conservative_products, aggressive_products, client_data, risk_profile) -> dict:
                # 确保输入参数不为None
                conservative_products = conservative_products or []
                aggressive_products = aggressive_products or []

                # 合并保守型和进取型产品
                all_filtered_products = conservative_products + aggressive_products

                # 如果没有产品，返回空列表
                if not all_filtered_products:
                    return {"matched_products": []}

                # 根据匹配度分数排序
                sorted_products = sorted(all_filtered_products, key=lambda p: p.get("match_score", 0), reverse=True)

                # 选择前10个产品（或者全部产品如果少于10个）
                top_products = sorted_products[:min(10, len(sorted_products))]

                # 添加产品组合建议
                portfolio_recommendation = generate_portfolio_recommendation(top_products, client_data, risk_profile)

                # 添加到结果中
                result = {
                    "matched_products": top_products,
                    "portfolio_recommendation": portfolio_recommendation
                }

                return result

            def generate_portfolio_recommendation(products, client_data, risk_profile):
                """Generate portfolio allocation recommendation based on client risk profile"""
                if not products:
                    return {}

                # 获取客户风险等级
                risk_level = risk_profile.get("risk_level", "R3")

                # 根据风险等级定义产品类型的分配比例
                allocation_by_risk = {
                    "R1": {  # 保守型
                        "cash_management": 0.3,
                        "fixed_income": 0.4,
                        "capital_protected": 0.2,
                        "low_risk_mixed": 0.1,
                        "other": 0.0
                    },
                    "R2": {  # 稳健型
                        "cash_management": 0.2,
                        "fixed_income": 0.3,
                        "capital_protected": 0.3,
                        "low_risk_mixed": 0.2,
                        "other": 0.0
                    },
                    "R3": {  # 平衡型
                        "cash_management": 0.1,
                        "fixed_income": 0.2,
                        "low_risk_mixed": 0.3,
                        "balanced_mixed": 0.3,
                        "bond_enhanced": 0.1,
                        "other": 0.0
                    },
                    "R4": {  # 进取型
                        "fixed_income": 0.1,
                        "balanced_mixed": 0.2,
                        "bond_enhanced": 0.2,
                        "equity_enhanced": 0.3,
                        "alternative_investment": 0.2,
                        "other": 0.0
                    },
                    "R5": {  # 激进型
                        "balanced_mixed": 0.1,
                        "equity_enhanced": 0.3,
                        "alternative_investment": 0.3,
                        "high_yield": 0.3,
                        "other": 0.0
                    }
                }

                # 获取当前风险等级的分配比例
                allocation = allocation_by_risk.get(risk_level, allocation_by_risk["R3"])

                # 根据客户数据调整分配比例
                # 例如，如果客户流动性需求高，增加现金管理类产品的比例
                liquidity_needs = client_data.get("liquidity_needs", "medium")
                if liquidity_needs == "high" and "cash_management" in allocation:
                    # 增加现金管理类产品的比例
                    allocation["cash_management"] = min(0.5, allocation["cash_management"] + 0.2)
                    # 调整其他类型的比例，确保总和为1
                    total_others = sum(v for k, v in allocation.items() if k != "cash_management")
                    if total_others > 0:
                        factor = (1 - allocation["cash_management"]) / total_others
                        for k in allocation:
                            if k != "cash_management":
                                allocation[k] *= factor

                # 根据实际可用产品调整分配
                available_types = set(p.get("type", "") for p in products)
                adjusted_allocation = {}
                for product_type, ratio in allocation.items():
                    if product_type in available_types or product_type == "other":
                        adjusted_allocation[product_type] = ratio

                # 如果没有可用的产品类型，将全部分配给"other"
                if not adjusted_allocation or sum(adjusted_allocation.values()) == 0:
                    adjusted_allocation = {"other": 1.0}

                # 标准化分配比例，确保总和为1
                total = sum(adjusted_allocation.values())
                if total > 0:
                    for k in adjusted_allocation:
                        adjusted_allocation[k] /= total

                # 计算每个产品类型的建议投资金额
                investment_amount = client_data.get("assets", 1000000) * 0.3  # 假设可投资金额为总资产的30%
                amount_by_type = {k: round(v * investment_amount, 2) for k, v in adjusted_allocation.items()}

                # 生成最终的组合建议
                portfolio = {
                    "risk_level": risk_level,
                    "total_investment_amount": investment_amount,
                    "allocation_ratio": adjusted_allocation,
                    "allocation_amount": amount_by_type,
                    "recommended_products": {}
                }

                # 为每个产品类型选择最匹配的产品
                for product_type in adjusted_allocation:
                    if product_type == "other":
                        continue

                    # 过滤当前类型的产品
                    type_products = [p for p in products if p.get("type", "") == product_type]
                    if type_products:
                        # 按匹配度排序
                        sorted_type_products = sorted(type_products, key=lambda p: p.get("match_score", 0), reverse=True)
                        # 选择最匹配的产品
                        top_product = sorted_type_products[0]
                        portfolio["recommended_products"][product_type] = {
                            "product_id": top_product.get("id", ""),
                            "product_name": top_product.get("name", ""),
                            "allocation_ratio": adjusted_allocation[product_type],
                            "allocation_amount": amount_by_type[product_type],
                            "match_score": top_product.get("match_score", 0)
                        }

                return portfolio
          context:
            enabled: true
            variable_selector:
              - conservative_filter
              - filtered_products
              - aggressive_filter
              - filtered_products
              - data_preprocessing
              - processed_data
              - risk_assessment
              - risk_profile
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: product_matching

      - data:
          type: llm
          title: 生成推荐理由
          model:
            provider: langgenius/openai_api_compatible/openai_api_compatible
            name: glm-4-flashx
            mode: chat
            completion_params:
              temperature: 0.7
          prompt_template:
            - id: system-prompt
              role: system
              text: '{{#context#}}

              你是一位专业的信托产品顾问，负责为客户生成个性化的信托产品推荐理由。你的推荐应该基于客户的风险偏好、投资目标、流动性需求和投资期限等因素。你的推荐应该专业、具体、个性化，并且清晰地解释产品如何满足客户的需求。'
            - id: user-prompt
              role: user
              text: |
                请根据以下客户信息和匹配的信托产品，生成个性化的推荐理由。

                ## 客户信息：
                - 年龄：{{#start_node.age#}}
                - 性别：{{#start_node.gender#}}
                - 月收入：{{#start_node.income#}}元
                - 总资产：{{#start_node.assets#}}元
                - 投资经验：{{#start_node.investment_experience#}}
                - 投资目标：{{#start_node.investment_goal#}}
                - 投资期限：{{#start_node.investment_horizon#}}
                - 风险承受能力：{{#start_node.risk_tolerance#}}
                - 流动性需求：{{#start_node.liquidity_needs#}}
                - 税务考量：{{#start_node.tax_considerations#}}
                - 特殊需求：{{#start_node.special_requirements#}}

                ## 风险评估结果：
                - 风险分数：{{#risk_assessment.risk_score#}}
                - 风险等级：{{#risk_assessment.risk_level#}}

                ## 匹配的信托产品：
                {{#product_matching.matched_products#}}

                ## 产品组合建议：
                {{#product_matching.portfolio_recommendation#}}

                请为每个推荐的产品生成个性化的推荐理由，包括以下内容：
                1. 产品如何匹配客户的风险偏好
                2. 产品如何满足客户的投资目标
                3. 产品如何符合客户的流动性需求
                4. 产品的独特优势
                5. 建议的配置比例及理由

                请以JSON格式输出，包含以下字段：
                ```json
                {
                  "products": [
                    {
                      "product_id": "产品ID",
                      "product_name": "产品名称",
                      "risk_match": "风险匹配分析",
                      "goal_match": "目标匹配分析",
                      "liquidity_match": "流动性匹配分析",
                      "unique_advantages": "独特优势",
                      "allocation_recommendation": "配置建议",
                      "personalized_reason": "个性化推荐理由"
                    }
                  ],
                  "portfolio_advice": "整体组合建议"
                }
                ```
          context:
            enabled: true
            variable_selector:
              - start_node
              - age
              - gender
              - income
              - assets
              - investment_experience
              - investment_goal
              - investment_horizon
              - risk_tolerance
              - liquidity_needs
              - tax_considerations
              - special_requirements
              - risk_assessment
              - risk_score
              - risk_level
              - risk_profile
              - product_matching
              - matched_products
              - portfolio_recommendation
          outputs:
            text:
              type: string
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: generate_recommendations

      - data:
          type: code
          title: 结果排序
          code_language: python3
          variables:
            - value_selector:
                - product_matching
                - matched_products
              variable: matched_products
            - value_selector:
                - product_matching
                - portfolio_recommendation
              variable: portfolio_recommendation
            - value_selector:
                - generate_recommendations
                - text
              variable: recommendations_text
          outputs:
            final_results:
              type: object
          code: |
            import json
            import re

            def main(matched_products, portfolio_recommendation, recommendations_text) -> dict:
                # 解析推荐理由JSON
                recommendations = extract_json(recommendations_text)

                # 如果无法解析，返回原始数据
                if not recommendations or "products" not in recommendations:
                    return {
                        "final_results": {
                            "matched_products": matched_products,
                            "portfolio_recommendation": portfolio_recommendation,
                            "recommendations": {"products": [], "portfolio_advice": ""}
                        }
                    }

                # 将推荐理由与匹配产品合并
                enriched_products = []
                for product in matched_products:
                    # 在推荐中查找对应产品
                    product_id = product.get("id", "")
                    product_recommendation = None

                    for rec in recommendations.get("products", []):
                        if rec.get("product_id", "") == product_id:
                            product_recommendation = rec
                            break

                    # 合并产品信息和推荐理由
                    enriched_product = product.copy()
                    if product_recommendation:
                        for key, value in product_recommendation.items():
                            if key not in ["product_id", "product_name"]:
                                enriched_product[key] = value

                    enriched_products.append(enriched_product)

                # 按匹配度降序排序
                sorted_products = sorted(enriched_products, key=lambda p: p.get("match_score", 0), reverse=True)

                # 构建最终结果
                final_results = {
                    "matched_products": sorted_products,
                    "portfolio_recommendation": portfolio_recommendation,
                    "portfolio_advice": recommendations.get("portfolio_advice", "")
                }

                return {"final_results": final_results}

            def extract_json(text):
                """Extract JSON from text"""
                try:
                    # 尝试直接解析整个文本为JSON
                    return json.loads(text)
                except:
                    # 如果失败，尝试从文本中提取JSON部分
                    json_match = re.search(r'```json\s*({[\s\S]*?})\s*```', text)
                    if json_match:
                        try:
                            return json.loads(json_match.group(1))
                        except:
                            pass

                    # 尝试其他可能的JSON格式
                    json_match = re.search(r'{[\s\S]*"products"[\s\S]*}', text)
                    if json_match:
                        try:
                            return json.loads(json_match.group(0))
                        except:
                            pass

                    return None
          context:
            enabled: true
            variable_selector:
              - product_matching
              - matched_products
              - portfolio_recommendation
              - generate_recommendations
              - text
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: result_sorting

      - data:
          type: llm
          title: 生成推荐报告
          model:
            provider: langgenius/openai_api_compatible/openai_api_compatible
            name: glm-4-flashx
            mode: chat
            completion_params:
              temperature: 0.5
          prompt_template:
            - id: system-prompt
              role: system
              text: '{{#context#}}

              你是一位专业的信托产品顾问，负责生成个性化的信托产品推荐报告。你的报告应该清晰、专业、结构化，并且易于客户理解。你必须严格按照指定的格式输出，确保每次生成的报告结构完全一致。'
            - id: user-prompt
              role: user
              text: |
                请根据以下客户信息和产品推荐结果，生成一份个性化的信托产品推荐报告。

                ## 客户信息：
                - 年龄：{{#start_node.age#}}
                - 性别：{{#start_node.gender#}}
                - 月收入：{{#start_node.income#}}元
                - 总资产：{{#start_node.assets#}}元
                - 投资经验：{{#start_node.investment_experience#}}
                - 投资目标：{{#start_node.investment_goal#}}
                - 投资期限：{{#start_node.investment_horizon#}}
                - 风险承受能力：{{#start_node.risk_tolerance#}}
                - 流动性需求：{{#start_node.liquidity_needs#}}
                - 税务考量：{{#start_node.tax_considerations#}}
                - 特殊需求：{{#start_node.special_requirements#}}

                ## 风险评估结果：
                - 风险分数：{{#risk_assessment.risk_score#}}
                - 风险等级：{{#risk_assessment.risk_level#}}

                ## 推荐结果：
                {{#result_sorting.final_results#}}

                请生成一份完整的信托产品推荐报告，必须严格按照以下格式输出：

                # 个性化信托产品推荐报告

                ## 1. 客户需求分析
                客户基本信息：{{#start_node.age#}}岁{{#start_node.gender#}}，月收入{{#start_node.income#}}元，总资产{{#start_node.assets#}}元。投资经验为{{#start_node.investment_experience#}}，风险承受能力为{{#start_node.risk_tolerance#}}，流动性需求为{{#start_node.liquidity_needs#}}。

                客户主要投资需求：
                - 需求一：[根据客户投资目标和风险偏好分析的需求]
                - 需求二：[根据客户流动性需求和投资期限分析的需求]
                - 需求三：[根据客户特殊需求和税务考量分析的需求]

                ## 2. 风险评估
                根据客户的年龄、收入、资产状况、投资经验和风险偏好等因素，系统评估客户的风险等级为**{{#risk_assessment.risk_level#}}**，风险分数为**{{#risk_assessment.risk_score#}}**。

                风险等级解释：
                - R1（保守型）：追求资金安全性，接受低收益、低波动性
                - R2（稳健型）：追求资金安全性的同时实现适度增值
                - R3（平衡型）：平衡风险与收益，接受中等波动性
                - R4（进取型）：追求较高收益，接受较大波动性
                - R5（激进型）：追求高收益，接受高波动性

                客户风险特征分析：
                [根据客户风险等级和各项因素进行分析]

                ## 3. 推荐产品组合
                根据客户的风险等级和投资需求，我们推荐以下信托产品组合：

                | 产品类型 | 配置比例 | 建议金额(元) | 代表产品 |
                |---------|---------|----------|----------|
                [根据组合建议生成产品类型配置表]

                总投资金额：[总投资金额]元

                ## 4. 推荐产品详情

                [列出前3个最匹配的产品详情，每个产品包含以下信息：]

                ### 4.1 [产品名称]
                - **产品类型**：[类型]
                - **风险等级**：[风险等级]
                - **预期收益**：[预期收益]
                - **最低投资金额**：[最低投资金额]元
                - **投资期限**：[期限]
                - **流动性**：[流动性级别]
                - **产品特点**：[产品特点]
                - **匹配度**：[匹配分数]/100
                - **推荐理由**：[个性化推荐理由]

                ### 4.2 [产品名称]
                [同上格式]

                ### 4.3 [产品名称]
                [同上格式]

                ## 5. 投资组合建议
                [根据组合建议和产品特点提供的整体组合建议]

                ## 6. 风险提示
                - 所有投资都有风险，请在投资前仔细阅读产品说明书。
                - 信托产品收益受多种因素影响，预期收益不代表实际收益。
                - 建议客户分散投资，不要将全部资金投入单一产品。
                - 根据市场变化和个人情况变化，定期调整投资组合。

                ## 7. 后续服务
                - 我们将提供定期的投资组合评估和调整建议。
                - 如有任何疑问，请随时联系您的专属理财顾问。
                - 我们将根据市场变化和您的需求变化，提供持续的产品更新和建议。

                请使用专业但易于理解的语言，确保每次生成的报告都严格遵循上述格式。不要改变表格结构、列表格式或标题层级。
          context:
            enabled: true
            variable_selector:
              - start_node
              - age
              - gender
              - income
              - assets
              - investment_experience
              - investment_goal
              - investment_horizon
              - risk_tolerance
              - liquidity_needs
              - tax_considerations
              - special_requirements
              - risk_assessment
              - risk_score
              - risk_level
              - risk_profile
              - result_sorting
              - final_results
          outputs:
            text:
              type: string
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: generate_report

      - data:
          type: end
          title: 结束
          outputs:
            - value_selector:
                - generate_report
                - text
              variable: trust_recommendation
          vision:
            enabled: false
          availablePrevNodes: []
          availableNextNodes: []
        id: end_node
